#!/usr/bin/env node

/**
 * 调试工具执行流程
 */

import fs from 'fs';
import path from 'path';

// 模拟完整的ExtDocsApiClient
class DebugExtDocsApiClient {
  constructor(projectRoot = process.cwd()) {
    this.projectRoot = projectRoot;
    this.baseUrl = '';
    this.apiKey = '';
  }

  npmPackageToLocalPath(packageName) {
    console.log(`📝 npmPackageToLocalPath: ${packageName}`);
    if (packageName.startsWith('@')) {
      const parts = packageName.split('/');
      if (parts.length === 2) {
        const result = parts[1];
        console.log(`   -> 转换结果: ${result}`);
        return result;
      }
    }
    console.log(`   -> 转换结果: ${packageName}`);
    return packageName;
  }

  getDocsBasePath() {
    console.log(`📍 getDocsBasePath 开始检测...`);
    const cwd = process.cwd();
    console.log(`   - 当前工作目录: ${cwd}`);
    
    const isDistExecution =
      cwd.includes('node_modules') ||
      fs.existsSync(path.join(cwd, 'dist', 'docs'));
    
    console.log(`   - 工作目录包含node_modules: ${cwd.includes('node_modules')}`);
    console.log(`   - dist/docs存在: ${fs.existsSync(path.join(cwd, 'dist', 'docs'))}`);
    console.log(`   - isDistExecution: ${isDistExecution}`);
    
    const docsBasePath = isDistExecution
      ? path.join(cwd, 'dist', 'docs')
      : path.join(this.projectRoot, 'src', 'docs');
    
    console.log(`   - 最终路径: ${docsBasePath}`);
    console.log(`   - 路径存在: ${fs.existsSync(docsBasePath)}`);
    
    return docsBasePath;
  }

  isNpmPackageName(query) {
    const result = /^(@[a-z0-9-~][a-z0-9-._~]*\/)?[a-z0-9-~][a-z0-9-._~]*$/.test(query);
    console.log(`🔍 isNpmPackageName("${query}"): ${result}`);
    return result;
  }

  async searchLocalNpmDocsForComponent(packageName, componentName) {
    console.log(`\n🔍 searchLocalNpmDocsForComponent 开始...`);
    console.log(`   - packageName: ${packageName}`);
    console.log(`   - componentName: ${componentName}`);
    
    try {
      const localPath = this.npmPackageToLocalPath(packageName);
      const docsBasePath = this.getDocsBasePath();
      const docsPath = path.join(docsBasePath, localPath);

      console.log(`   - localPath: ${localPath}`);
      console.log(`   - docsBasePath: ${docsBasePath}`);
      console.log(`   - docsPath: ${docsPath}`);

      if (!fs.existsSync(docsPath)) {
        console.log(`❌ 路径不存在: ${docsPath}`);
        const result = {
          success: false,
          data: [],
          total: 0,
          message: `本地文档路径不存在: ${localPath}    ${docsPath}`,
        };
        console.log(`   -> 返回结果:`, result);
        return result;
      }

      console.log(`✅ 路径存在，开始扫描文件...`);
      const files = fs.readdirSync(docsPath);
      console.log(`   - 所有文件: ${files.join(', ')}`);
      
      const matchingFiles = files.filter(file => {
        const fileName = file.replace('.md', '').toLowerCase();
        const lowerComponentName = componentName.toLowerCase();
        const matches = fileName.includes(lowerComponentName) || fileName === lowerComponentName;
        console.log(`     检查文件 "${file}": fileName="${fileName}", componentName="${lowerComponentName}", matches=${matches}`);
        return matches;
      });

      console.log(`   - 匹配的文件: ${matchingFiles.join(', ')}`);

      if (matchingFiles.length > 0) {
        const results = matchingFiles.map(file => {
          const filePath = path.join(docsPath, file);
          const content = fs.readFileSync(filePath, 'utf-8');
          return {
            id: `local-${packageName}-${file}`,
            title: file.replace('.md', ''),
            content: content.substring(0, 200) + '...',
            source: `本地文档 (${packageName})`,
            type: 'component',
            description: `${packageName} ${componentName} 组件文档`
          };
        });

        const result = {
          success: true,
          data: results,
          total: results.length,
          message: `从本地文档找到 ${componentName} 组件的 ${results.length} 个相关文档`,
        };
        console.log(`✅ 成功找到文档:`, result);
        return result;
      } else {
        const result = {
          success: false,
          data: [],
          total: 0,
          message: `在 ${packageName} 中未找到 ${componentName} 组件的文档`,
        };
        console.log(`❌ 未找到匹配文档:`, result);
        return result;
      }
    } catch (error) {
      console.error(`💥 searchLocalNpmDocsForComponent 异常:`, error);
      const result = {
        success: false,
        data: [],
        total: 0,
        message: `搜索本地文档时发生错误: ${error.message}`,
      };
      console.log(`   -> 异常返回结果:`, result);
      return result;
    }
  }

  // 模拟完整的工具调用逻辑
  async simulateFullToolCall(params) {
    console.log(`\n🎯 开始模拟完整工具调用...`);
    console.log(`📥 输入参数:`, JSON.stringify(params, null, 2));
    
    try {
      const { componentNames, sources, limit = 10 } = params;
      let results = [];

      console.log(`📊 解析参数:`);
      console.log(`   - componentNames: ${JSON.stringify(componentNames)}`);
      console.log(`   - sources: ${JSON.stringify(sources)}`);
      console.log(`   - limit: ${limit}`);

      if (componentNames && componentNames.length > 0) {
        console.log(`\n🔍 开始查询指定组件...`);

        // 检查sources中是否有npm包名
        const npmPackageSources = sources?.filter(source => this.isNpmPackageName(source)) || [];
        const otherSources = sources?.filter(source => !this.isNpmPackageName(source)) || [];

        console.log(`📦 npm包源: ${JSON.stringify(npmPackageSources)}`);
        console.log(`🌐 其他源: ${JSON.stringify(otherSources)}`);

        for (const docName of componentNames) {
          console.log(`\n🔍 处理组件: ${docName}`);
          let foundInLocal = false;

          // 如果sources中有npm包名，优先在对应的本地目录中搜索特定组件
          if (npmPackageSources.length > 0) {
            console.log(`📦 在npm包源中搜索...`);

            for (const packageName of npmPackageSources) {
              console.log(`\n📋 搜索包: ${packageName}`);
              const localResult = await this.searchLocalNpmDocsForComponent(packageName, docName);

              if (localResult.success && localResult.data.length > 0) {
                console.log(`✅ 在包 ${packageName} 中找到组件 ${docName}`);
                results.push(...localResult.data);
                foundInLocal = true;
                break;
              } else {
                console.log(`❌ 在包 ${packageName} 中未找到组件 ${docName}: ${localResult.message}`);
              }
            }
          }

          if (!foundInLocal) {
            console.log(`❌ 在所有npm包源中都未找到组件 ${docName}`);
          }
        }
      }

      console.log(`\n📊 最终结果统计:`);
      console.log(`   - 找到的结果数量: ${results.length}`);
      console.log(`   - 结果详情:`, JSON.stringify(results, null, 2));

      const responseText = `# 远端文档查询结果

📊 查询统计：找到 ${results.length} 个相关文档

${results.map(doc => `
## ${doc.title}
${doc.description}

${doc.content}
`).join('\n')}`;

      const finalResult = {
        content: [
          {
            type: 'text',
            text: responseText,
          },
        ],
      };

      console.log(`\n✅ 工具调用完成，返回结果:`, JSON.stringify(finalResult, null, 2));
      return finalResult;

    } catch (error) {
      console.error(`💥 工具调用异常:`, error);
      const errorResult = {
        isError: true,
        content: [
          {
            type: 'text',
            text: `查询远端文档时发生错误: ${error.message}`,
          },
        ],
      };
      console.log(`   -> 异常返回结果:`, JSON.stringify(errorResult, null, 2));
      return errorResult;
    }
  }
}

async function runDebugTest() {
  console.log('🧪 开始调试工具执行流程\n');
  
  const client = new DebugExtDocsApiClient();
  
  // 测试实际的查询参数
  const testParams = {
    componentNames: ["AiInviteModal"],
    sources: ["@nibfe/crm-pc-react-components"]
  };
  
  const result = await client.simulateFullToolCall(testParams);
  
  console.log('\n🎯 最终测试结果:');
  console.log(JSON.stringify(result, null, 2));
}

runDebugTest().catch(console.error);
