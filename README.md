# 🔍 AI友好文档查询MCP

> 一个专为AI优化的 Model Context Protocol (MCP) 工具，提供智能化的文档查询和项目分析功能

[![npm version](https://badge.fury.io/js/ai-friendly-knowledge-mcp.svg)](https://badge.fury.io/js/ai-friendly-knowledge-mcp)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18-brightgreen.svg)](https://nodejs.org/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## ✨ 核心特性

### 🎯 智能文档分析
- **📁 本地文档扫描**: 自动扫描项目中的所有 Markdown 文档，按目录结构智能分类
- **🧩 存量组件检测**: 自动识别项目中的组件、工具类、Hook 等代码资源
- **🔍 智能搜索**: 支持关键词匹配、模糊搜索、批量查询等多种检索方式
- **📊 项目分析**: 提供详细的文档统计和项目结构分析

### 🛠️ 四大核心工具

1. **📁 本地文档智能搜索 (`search_local_docs`)**
   - 🔍 自动扫描项目中所有 `.md` 文档
   - 📂 按文件夹层级自动分类组织
   - 🎯 支持关键词、模糊匹配、批量查询
   - 📊 提供文档统计和结构化展示

2. **🧩 存量组件扫描器 (`scan_project_components`)**
   - 🔍 智能识别 React/Vue 组件、工具类、Hook
   - 📋 按类型分类展示（组件/工具/Hook/服务/类型）
   - 📊 提供详细的项目代码统计分析
   - 🎯 专门处理"当前仓库有哪些存量组件"类查询

3. **🌐 外部文档智能搜索 (`search_external_docs`)**
   - 🔍 查询第三方组件库文档 (Ant Design, Element UI 等)
   - 🌐 支持多源文档检索和相关性排序
   - 📚 智能推荐相关文档和最佳实践
   - 🔗 从远端服务器获取最新文档内容

4. **📦 NPM包文档搜索 (`search_npm_docs`)**
   - 📦 查询项目依赖的 npm 包文档
   - 📖 返回包的 README 和基本信息
   - 🔍 智能过滤和批量查询
   - ⚡ 支持并发处理提升查询效率

## 🚀 快速开始

### 📦 安装

**要求**: Node.js ≥ 18

```bash
# NPM
npm install -g ai-friendly-knowledge-mcp

# Yarn
yarn global add ai-friendly-knowledge-mcp

# PNPM
pnpm add -g ai-friendly-knowledge-mcp
```

### ⚙️ MCP 客户端配置

#### 🎯 Cursor/Windsurf (推荐)

**方法1: 使用 npx (最简单)**
```json
{
  "mcpServers": {
    "ai-friendly-knowledge-mcp": {
      "command": "npx",
      "args": ["-y", "ai-friendly-knowledge-mcp"],
      "cwd": "/path/to/your/project"
    }
  }
}
```

**方法2: 指定项目根目录**
```json
{
  "mcpServers": {
    "ai-friendly-knowledge-mcp": {
      "command": "npx",
      "args": ["-y", "ai-friendly-knowledge-mcp", "--project-root", "/path/to/your/project"]
    }
  }
}
```

**方法3: 使用环境变量**
```json
{
    "mcpServers": {
        "ai-friendly-knowledge-mcp": {
      "command": "npx",
      "args": ["-y", "ai-friendly-knowledge-mcp"],
      "env": {
        "PROJECT_ROOT": "/path/to/your/project"
      }
    }
  }
}
```

#### 🔧 配置说明

| 参数 | 说明 | 示例 |
|------|------|------|
| `cwd` | 工作目录，MCP 会扫描此目录下的文档 | `"/path/to/your/project"` |
| `--project-root` | 通过命令行参数指定项目根目录 | `"--project-root", "/path/to/project"` |
| `PROJECT_ROOT` | 通过环境变量指定项目根目录 | `"PROJECT_ROOT": "/path/to/project"` |
| `--source` | 指定外部文档源 | `"--source", "antd"` |

**优先级**: 命令行参数 > 环境变量 > cwd > 自动检测



## 🎯 AI 智能触发

### 🚀 快速使用

在 Cursor 中只需要自然地询问项目问题，AI 会自动调用相应的 MCP 工具：

```txt
# 这些提问会自动触发 search_local_docs
"当前仓库有哪些存量组件？"
"项目有什么工具类？"
"查看项目文档"

# 这些提问会自动触发 scan_project_components  
"当前项目的组件结构是什么样的？"
"有哪些可复用的组件？"

# 这些提问会自动触发 search_external_docs
"Ant Design 的 Button 组件怎么用？"
"Element UI Table 有哪些属性？"

# 这些提问会自动触发 search_npm_docs
"lodash 有哪些工具函数？"
"项目依赖的包文档"
```

### 🛠️ 工具参考

| 工具名称 | 触发场景 | 参数示例 |
|---------|---------|----------|
| `search_local_docs` | 查询项目文档 | `["组件", "工具", "API"]` |
| `scan_project_components` | 扫描项目组件 | `["components", "utils"]` |
| `search_external_docs` | 查询外部文档 | `["Button", "Table"]` |
| `search_npm_docs` | 查询 NPM 包 | `["lodash", "axios"]` |

### 📋 配置选项

```bash
# 指定外部文档源
npx ai-friendly-knowledge-mcp --source antd --source element-ui

# 指定项目根目录
npx ai-friendly-knowledge-mcp --project-root /path/to/project

# 指定端口（如果需要）
npx ai-friendly-knowledge-mcp --port 4000

# 组合使用
npx ai-friendly-knowledge-mcp --project-root . --source antd --source element-ui
```

## 🔧 开发者指南

### 📁 项目结构

```
ai-friendly-knowledge-mcp/
├── src/                          # 源代码目录
│   ├── tools/                    # MCP 工具实现
│   │   ├── localDocsQuery.ts     # 本地文档查询
│   │   ├── componentScanner.ts   # 组件扫描器
│   │   ├── extDocsQuery.ts       # 外部文档查询
│   │   └── npmDocsQuery.ts       # NPM 包查询
│   ├── utils/                    # 工具函数
│   │   ├── documentManager.ts    # 文档管理器
│   │   └── projectDetection.ts   # 项目检测
│   ├── config.ts                 # 配置管理
│   ├── server.ts                 # MCP 服务器
│   └── index.ts                  # 入口文件
├── bin/cli.js                    # CLI 入口
├── docs.json                     # 文档配置
└── README.md                     # 项目文档
```

### 🛠️ 本地开发

```bash
# 克隆项目
git clone https://github.com/your-username/ai-friendly-knowledge-mcp.git
cd ai-friendly-knowledge-mcp

# 安装依赖
npm install

# 构建项目
npm run build

# 启动开发模式
npm run dev

# 测试 CLI
node bin/cli.js --help
```

### ⚙️ 环境变量配置

创建 `.env` 文件：

```env
# 项目根目录（可选）
PROJECT_ROOT=/path/to/your/project

# 外部文档源（可选）
DOC_SOURCES=antd,element-ui,material-ui

# 远端文档 API（可选）
EXT_DOCS_API_URL=https://your-docs-api.com
EXT_DOCS_API_KEY=your_api_key

# 资源描述 API（可选）
RESOURCE_API_BASE_URL=https://your-resource-api.com
RESOURCE_API_KEY=your_resource_api_key

# 服务器端口（可选）
PORT=3333
```

## 📊 特性亮点

### 🧠 智能项目分析
- **自动检测项目类型**: 自动识别 React、Vue、Node.js 等项目类型
- **智能文档分类**: 按目录结构和内容自动分类文档
- **代码资源识别**: 自动识别组件、工具类、Hook、服务等代码资源

### 🔍 强大的搜索能力
- **多种搜索模式**: 精确匹配、模糊搜索、包含搜索
- **批量查询**: 支持一次查询多个关键词
- **智能建议**: 自动提供相关文档推荐

### 📈 性能优化
- **一次扫描**: 服务器启动时一次性扫描，避免重复操作
- **增量更新**: 支持文档变更的增量更新
- **并发处理**: NPM 包查询支持并发处理

### 🎯 AI 友好设计
- **自动触发**: 根据用户问题自动选择合适的工具
- **结构化输出**: 提供清晰的结构化查询结果
- **智能参数推断**: 自动从用户问题中提取查询参数

## 🚀 最佳实践

### 📝 文档组织建议

1. **使用标准 Markdown 格式**
   ```markdown
   ---
   title: "组件名称"
   description: "组件描述"
   tags: ["UI", "组件"]
   ---
   
   # 组件标题
   
   ## 功能特性
   ## 使用方法
   ## API 参考
   ```

2. **合理的目录结构**
   ```
   docs/
   ├── components/        # 组件文档
   ├── utils/            # 工具文档
   ├── guides/           # 指南文档
   └── api/              # API 文档
   ```

### 🔧 配置优化

1. **使用 `docs.json` 配置文件**
   ```json
   {
     "name": "项目文档库",
     "description": "项目文档集合",
     "docs": [
       {
         "name": "component-guide",
         "title": "组件开发指南",
         "description": "组件开发规范和最佳实践",
         "path": "docs/components/guide.md"
       }
     ]
}
```

2. **合理设置项目根目录**
   - 优先使用 `cwd` 设置工作目录
   - 必要时使用命令行参数 `--project-root`
   - 利用自动检测功能减少配置

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

### 🐛 报告问题
- 使用 Issue 模板
- 提供详细的错误信息和重现步骤
- 附上相关的配置和环境信息

### 💡 功能建议
- 描述具体的使用场景
- 说明预期的功能行为
- 提供示例和参考资料

### 🔧 代码贡献
1. Fork 本仓库
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 提交 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

感谢所有贡献者和社区的支持！

---

> 💡 **提示**: 如果您在使用过程中遇到任何问题，请查看 [Issues](https://github.com/your-username/ai-friendly-knowledge-mcp/issues) 或创建新的 Issue。