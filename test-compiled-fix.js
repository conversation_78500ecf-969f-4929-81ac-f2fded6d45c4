#!/usr/bin/env node

/**
 * 测试编译后的修复功能
 */

import fs from 'fs';
import path from 'path';

// 模拟ExtDocsApiClient的关键方法
class TestCompiledFix {
  constructor(projectRoot = process.cwd()) {
    this.projectRoot = projectRoot;
  }

  npmPackageToLocalPath(packageName) {
    if (packageName.startsWith('@')) {
      const parts = packageName.split('/');
      if (parts.length === 2) {
        return parts[1];
      }
    }
    return packageName;
  }

  getDocsBasePath() {
    const isDistExecution =
      process.cwd().includes('node_modules') ||
      fs.existsSync(path.join(process.cwd(), 'dist', 'docs'));
    
    const docsBasePath = isDistExecution
      ? path.join(process.cwd(), 'dist', 'docs')
      : path.join(this.projectRoot, 'src', 'docs');
    
    return docsBasePath;
  }

  async searchLocalNpmDocs(packageName) {
    try {
      const localPath = this.npmPackageToLocalPath(packageName);
      const docsBasePath = this.getDocsBasePath();
      const docsPath = path.join(docsBasePath, localPath);

      console.log(`🔍 搜索本地npm包文档: ${packageName}`);
      console.log(`   - 本地路径: ${localPath}`);
      console.log(`   - 文档基础路径: ${docsBasePath}`);
      console.log(`   - 完整文档路径: ${docsPath}`);

      if (!fs.existsSync(docsPath)) {
        console.log(`❌ 本地文档路径不存在: ${docsPath}`);
        return {
          success: false,
          data: [],
          total: 0,
          message: `本地文档路径不存在: ${localPath}    ${docsPath}`,
        };
      }

      const files = fs.readdirSync(docsPath).filter(file => file.endsWith('.md'));
      console.log(`✅ 找到 ${files.length} 个Markdown文件`);

      const results = files.map(file => {
        const filePath = path.join(docsPath, file);
        const content = fs.readFileSync(filePath, 'utf-8');
        
        return {
          id: `local-${packageName}-${file}`,
          title: file.replace('.md', ''),
          content: content.substring(0, 200) + '...', // 只显示前200个字符
          source: `本地文档 (${packageName})`,
          type: 'component',
          description: `${packageName} 组件文档`
        };
      });

      return {
        success: true,
        data: results,
        total: results.length,
        message: `从本地文档找到 ${results.length} 个相关文档`,
      };

    } catch (error) {
      console.error(`❌ 搜索失败:`, error.message);
      return {
        success: false,
        data: [],
        total: 0,
        message: `搜索本地文档时发生错误: ${error.message}`,
      };
    }
  }

  async searchLocalNpmDocsForComponent(packageName, componentName) {
    try {
      const localPath = this.npmPackageToLocalPath(packageName);
      const docsBasePath = this.getDocsBasePath();
      const docsPath = path.join(docsBasePath, localPath);

      console.log(`🔍 搜索组件文档: ${packageName} -> ${componentName}`);
      console.log(`   - 完整文档路径: ${docsPath}`);

      if (!fs.existsSync(docsPath)) {
        console.log(`❌ 本地文档路径不存在: ${docsPath}`);
        return {
          success: false,
          data: [],
          total: 0,
          message: `本地文档路径不存在: ${localPath}    ${docsPath}`,
        };
      }

      const files = fs.readdirSync(docsPath);
      const matchingFiles = files.filter(file => {
        const fileName = file.replace('.md', '').toLowerCase();
        const lowerComponentName = componentName.toLowerCase();
        return fileName.includes(lowerComponentName) || fileName === lowerComponentName;
      });

      console.log(`✅ 找到 ${matchingFiles.length} 个匹配的文件: ${matchingFiles.join(', ')}`);

      const results = matchingFiles.map(file => {
        const filePath = path.join(docsPath, file);
        const content = fs.readFileSync(filePath, 'utf-8');
        
        return {
          id: `local-${packageName}-${file}`,
          title: file.replace('.md', ''),
          content: content.substring(0, 300) + '...', // 显示前300个字符
          source: `本地文档 (${packageName})`,
          type: 'component',
          description: `${packageName} ${componentName} 组件文档`
        };
      });

      return {
        success: results.length > 0,
        data: results,
        total: results.length,
        message: results.length > 0 
          ? `从本地文档找到 ${componentName} 组件的 ${results.length} 个相关文档`
          : `在 ${packageName} 中未找到 ${componentName} 组件的文档`,
      };

    } catch (error) {
      console.error(`❌ 搜索组件文档失败:`, error.message);
      return {
        success: false,
        data: [],
        total: 0,
        message: `搜索本地文档时发生错误: ${error.message}`,
      };
    }
  }
}

async function runTest() {
  console.log('🧪 测试编译后的修复功能\n');
  
  const client = new TestCompiledFix();
  
  // 测试1: 搜索整个包的文档
  console.log('=== 测试1: 搜索@nibfe/crm-pc-react-components包文档 ===');
  const result1 = await client.searchLocalNpmDocs('@nibfe/crm-pc-react-components');
  console.log('📊 结果:', JSON.stringify(result1, null, 2));
  
  console.log('\n=== 测试2: 搜索特定组件AiInviteModal ===');
  const result2 = await client.searchLocalNpmDocsForComponent('@nibfe/crm-pc-react-components', 'AiInviteModal');
  console.log('📊 结果:', JSON.stringify(result2, null, 2));
  
  console.log('\n=== 测试3: 搜索不存在的组件 ===');
  const result3 = await client.searchLocalNpmDocsForComponent('@nibfe/crm-pc-react-components', 'NonExistentComponent');
  console.log('📊 结果:', JSON.stringify(result3, null, 2));
  
  console.log('\n✅ 所有测试完成');
}

runTest().catch(console.error);
