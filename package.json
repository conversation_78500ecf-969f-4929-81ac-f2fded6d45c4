{"name": "ai-friendly-knowledge-mcp", "version": "1.0.0", "description": "AI友好文档查询MCP工具 - 提供本地文档、远端文档和npm包文档的智能查询功能", "main": "dist/index.js", "type": "module", "bin": {"ai-friendly-knowledge-mcp": "./bin/cli.js"}, "scripts": {"build": "rm -rf dist && tsc && cp -r ./src/docs ./dist/docs && chmod +x ./bin/cli.js", "start": "node dist/index.js", "dev": "tsx src/index.ts", "clean": "rm -rf dist", "prepublishOnly": "npm run build", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,md}", "type-check": "tsc --noEmit"}, "keywords": ["mcp", "docs", "documentation", "ai-friendly", "query", "npm", "knowledge-base"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.4", "dotenv": "^16.4.5", "yargs": "^17.7.2", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22.8.4", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitest/coverage-v8": "^1.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "tsx": "^4.19.1", "typescript": "^5.6.3", "vitest": "^1.0.0"}, "engines": {"node": ">=18"}}