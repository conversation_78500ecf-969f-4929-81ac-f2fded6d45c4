#!/usr/bin/env node

/**
 * AI友好文档查询MCP命令行入口点
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 导入并运行主程序
const indexPath = join(__dirname, '..', 'dist', 'index.js');

try {
  await import(indexPath);
} catch (error) {
  console.error('错误：无法启动AI友好文档查询MCP服务器');
  console.error('请确保已运行 "npm run build" 构建项目');
  console.error('详细错误信息:', error.message);
  process.exit(1);
}