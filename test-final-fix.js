#!/usr/bin/env node

/**
 * 测试最终修复结果
 */

import fs from 'fs';
import path from 'path';

// 模拟ExtDocsApiClient的完整逻辑
class FinalTestClient {
  constructor(projectRoot = process.cwd()) {
    this.projectRoot = projectRoot;
  }

  npmPackageToLocalPath(packageName) {
    if (packageName.startsWith('@')) {
      const parts = packageName.split('/');
      if (parts.length === 2) {
        return parts[1];
      }
    }
    return packageName;
  }

  getDocsBasePath() {
    const isDistExecution =
      process.cwd().includes('node_modules') ||
      fs.existsSync(path.join(process.cwd(), 'dist', 'docs'));
    
    const docsBasePath = isDistExecution
      ? path.join(process.cwd(), 'dist', 'docs')
      : path.join(this.projectRoot, 'src', 'docs');
    
    console.log(`📍 文档基础路径检测: isDistExecution=${isDistExecution}, docsBasePath=${docsBasePath}`);
    
    return docsBasePath;
  }

  isNpmPackageName(query) {
    return /^(@[a-z0-9-~][a-z0-9-._~]*\/)?[a-z0-9-~][a-z0-9-._~]*$/.test(query);
  }

  async searchLocalNpmDocsForComponent(packageName, componentName) {
    try {
      const localPath = this.npmPackageToLocalPath(packageName);
      const docsBasePath = this.getDocsBasePath();
      const docsPath = path.join(docsBasePath, localPath);

      console.log(`🔍 在本地npm包中搜索组件: ${packageName} -> ${componentName} -> ${docsPath}`);

      if (!fs.existsSync(docsPath)) {
        console.log(`❌ 本地文档路径不存在: ${docsPath}`);
        return {
          success: false,
          data: [],
          total: 0,
          message: `本地文档路径不存在: ${localPath}    ${docsPath}`,
        };
      }

      const files = fs.readdirSync(docsPath);
      const matchingFiles = files.filter(file => {
        const fileName = file.replace('.md', '').toLowerCase();
        const lowerComponentName = componentName.toLowerCase();
        return fileName.includes(lowerComponentName) || fileName === lowerComponentName;
      });

      console.log(`✅ 找到 ${matchingFiles.length} 个匹配的文件: ${matchingFiles.join(', ')}`);

      if (matchingFiles.length > 0) {
        const results = matchingFiles.map(file => ({
          id: `local-${packageName}-${file}`,
          title: file.replace('.md', ''),
          content: `组件文档内容: ${file}`,
          source: `本地文档 (${packageName})`,
          type: 'component',
          description: `${packageName} ${componentName} 组件文档`
        }));

        return {
          success: true,
          data: results,
          total: results.length,
          message: `从本地文档找到 ${componentName} 组件的 ${results.length} 个相关文档`,
        };
      } else {
        return {
          success: false,
          data: [],
          total: 0,
          message: `在 ${packageName} 中未找到 ${componentName} 组件的文档`,
        };
      }
    } catch (error) {
      console.error(`❌ 搜索本地npm包组件文档失败: ${packageName}/${componentName}`, error);
      return {
        success: false,
        data: [],
        total: 0,
        message: `搜索本地文档时发生错误: ${error.message}`,
      };
    }
  }

  // 模拟完整的工具调用逻辑
  async simulateToolCall(componentNames, sources) {
    console.log(`\n🎯 模拟工具调用: componentNames=${JSON.stringify(componentNames)}, sources=${JSON.stringify(sources)}`);
    
    let results = [];

    if (componentNames && componentNames.length > 0) {
      // 检查sources中是否有npm包名
      const npmPackageSources = sources?.filter(source => this.isNpmPackageName(source)) || [];
      
      console.log(`📦 检测到npm包源: ${npmPackageSources.join(', ')}`);

      for (const docName of componentNames) {
        let foundInLocal = false;

        // 如果sources中有npm包名，优先在对应的本地目录中搜索特定组件
        if (npmPackageSources.length > 0) {
          console.log(`🔍 在npm包源中搜索组件: ${docName}`);

          for (const packageName of npmPackageSources) {
            const localResult = await this.searchLocalNpmDocsForComponent(packageName, docName);

            if (localResult.success && localResult.data.length > 0) {
              // 本地找到了文档，使用本地结果
              results.push(...localResult.data);
              foundInLocal = true;
              console.log(`✅ 在本地包 ${packageName} 中找到组件 ${docName}，找到 ${localResult.data.length} 个结果`);
              break; // 找到就停止搜索其他包
            } else {
              // 记录搜索失败的详细信息，但不直接返回
              console.log(`⚠️ 在本地包 ${packageName} 中未找到组件 ${docName}: ${localResult.message}`);
            }
          }
        }

        if (!foundInLocal) {
          console.log(`❌ 在所有npm包源中都未找到组件 ${docName}`);
        }
      }
    }

    return {
      success: results.length > 0,
      data: results,
      total: results.length,
      message: results.length > 0 
        ? `找到 ${results.length} 个相关文档`
        : '未找到相关文档'
    };
  }
}

async function runFinalTest() {
  console.log('🧪 测试最终修复结果\n');
  
  const client = new FinalTestClient();
  
  // 测试场景1: 成功找到组件
  console.log('=== 测试场景1: 查询存在的组件 ===');
  const result1 = await client.simulateToolCall(['AiInviteModal'], ['@nibfe/crm-pc-react-components']);
  console.log('📊 结果1:', JSON.stringify(result1, null, 2));
  
  // 测试场景2: 查询不存在的组件
  console.log('\n=== 测试场景2: 查询不存在的组件 ===');
  const result2 = await client.simulateToolCall(['NonExistentComponent'], ['@nibfe/crm-pc-react-components']);
  console.log('📊 结果2:', JSON.stringify(result2, null, 2));
  
  // 测试场景3: 查询不存在的包
  console.log('\n=== 测试场景3: 查询不存在的包 ===');
  const result3 = await client.simulateToolCall(['AnyComponent'], ['@test/non-existent-package']);
  console.log('📊 结果3:', JSON.stringify(result3, null, 2));
  
  console.log('\n✅ 所有测试完成');
}

runFinalTest().catch(console.error);
