#!/usr/bin/env node

/**
 * 调试运行时环境
 */

import fs from 'fs';
import path from 'path';

function debugRuntimeEnvironment() {
  console.log('🔍 调试运行时环境\n');
  
  const cwd = process.cwd();
  console.log(`当前工作目录: ${cwd}`);
  console.log(`工作目录包含node_modules: ${cwd.includes('node_modules')}`);
  
  // 检查各种可能的路径
  const paths = [
    path.join(cwd, 'dist', 'docs'),
    path.join(cwd, 'src', 'docs'),
    path.join(cwd, 'dist', 'docs', 'crm-pc-react-components'),
    path.join(cwd, 'src', 'docs', 'crm-pc-react-components')
  ];
  
  console.log('\n📁 路径检查:');
  paths.forEach(p => {
    console.log(`  ${fs.existsSync(p) ? '✅' : '❌'} ${p}`);
  });
  
  // 模拟getDocsBasePath逻辑
  const isDistExecution = cwd.includes('node_modules') || fs.existsSync(path.join(cwd, 'dist', 'docs'));
  const docsBasePath = isDistExecution
    ? path.join(cwd, 'dist', 'docs')
    : path.join(cwd, 'src', 'docs');
  
  console.log(`\n🎯 路径检测结果:`);
  console.log(`  - isDistExecution: ${isDistExecution}`);
  console.log(`  - docsBasePath: ${docsBasePath}`);
  console.log(`  - 基础路径存在: ${fs.existsSync(docsBasePath)}`);
  
  // 检查npm包路径
  const packageName = '@nibfe/crm-pc-react-components';
  const localPath = packageName.split('/')[1]; // crm-pc-react-components
  const docsPath = path.join(docsBasePath, localPath);
  
  console.log(`\n📦 NPM包路径检查:`);
  console.log(`  - 包名: ${packageName}`);
  console.log(`  - 本地路径: ${localPath}`);
  console.log(`  - 完整路径: ${docsPath}`);
  console.log(`  - 路径存在: ${fs.existsSync(docsPath)}`);
  
  if (fs.existsSync(docsPath)) {
    const files = fs.readdirSync(docsPath);
    console.log(`  - 文件数量: ${files.length}`);
    console.log(`  - 文件列表: ${files.join(', ')}`);
  }
  
  // 检查环境变量
  console.log(`\n🌍 环境变量:`);
  console.log(`  - NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
  console.log(`  - PWD: ${process.env.PWD || 'undefined'}`);
  
  // 检查进程信息
  console.log(`\n⚙️ 进程信息:`);
  console.log(`  - process.argv[0]: ${process.argv[0]}`);
  console.log(`  - process.argv[1]: ${process.argv[1]}`);
  console.log(`  - __dirname: ${import.meta.url}`);
  
  // 模拟实际的错误场景
  console.log(`\n🚨 模拟错误场景:`);
  
  // 如果使用src/docs路径会怎样
  const srcDocsPath = path.join(cwd, 'src', 'docs', localPath);
  console.log(`  - src路径: ${srcDocsPath}`);
  console.log(`  - src路径存在: ${fs.existsSync(srcDocsPath)}`);
  
  // 如果使用dist/docs路径会怎样
  const distDocsPath = path.join(cwd, 'dist', 'docs', localPath);
  console.log(`  - dist路径: ${distDocsPath}`);
  console.log(`  - dist路径存在: ${fs.existsSync(distDocsPath)}`);
  
  // 检查可能的问题
  console.log(`\n🔧 可能的问题:`);
  if (!fs.existsSync(path.join(cwd, 'dist', 'docs'))) {
    console.log(`  ⚠️ dist/docs目录不存在，需要运行构建`);
  }
  if (!fs.existsSync(distDocsPath) && fs.existsSync(srcDocsPath)) {
    console.log(`  ⚠️ dist/docs中没有npm包文档，但src/docs中有`);
  }
  if (cwd.includes('node_modules')) {
    console.log(`  ⚠️ 当前在node_modules中运行，可能是全局安装`);
  }
}

debugRuntimeEnvironment();
