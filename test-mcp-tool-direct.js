#!/usr/bin/env node

/**
 * 直接测试MCP工具调用
 */

import { registerExtDocsQueryTool } from './dist/tools/extDocsQuery.js';

// 模拟MCP服务器
class MockMcpServer {
  constructor() {
    this.tools = new Map();
  }

  tool(name, description, schema, handler) {
    console.log(`📝 注册工具: ${name}`);
    console.log(`📄 描述: ${description.substring(0, 100)}...`);
    console.log(`🔧 参数模式:`, Object.keys(schema));
    
    this.tools.set(name, {
      name,
      description,
      schema,
      handler
    });
  }

  async callTool(name, params) {
    console.log(`\n🎯 调用工具: ${name}`);
    console.log(`📥 参数:`, JSON.stringify(params, null, 2));
    
    const tool = this.tools.get(name);
    if (!tool) {
      throw new Error(`工具 ${name} 不存在`);
    }

    try {
      const result = await tool.handler(params);
      console.log(`✅ 工具调用成功`);
      console.log(`📤 返回结果:`, JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      console.error(`❌ 工具调用失败:`, error);
      throw error;
    }
  }

  listTools() {
    return Array.from(this.tools.keys());
  }
}

async function testMcpToolDirect() {
  console.log('🧪 直接测试MCP工具调用\n');
  
  // 创建模拟服务器
  const server = new MockMcpServer();
  
  // 注册工具
  console.log('📋 注册外部文档查询工具...');
  registerExtDocsQueryTool(server, []);
  
  console.log(`\n📊 已注册的工具: ${server.listTools().join(', ')}`);
  
  // 测试工具调用
  const testParams = {
    componentNames: ["AiInviteModal"],
    sources: ["@nibfe/crm-pc-react-components"],
    limit: 10
  };
  
  try {
    const result = await server.callTool('search_external_docs', testParams);
    
    console.log('\n🎯 最终测试结果:');
    if (result && result.content && result.content[0] && result.content[0].text) {
      const text = result.content[0].text;
      console.log('📄 返回文本内容:');
      console.log(text);
      
      // 检查是否包含预期的内容
      if (text.includes('AiInviteModal')) {
        console.log('\n✅ 测试成功：返回内容包含AiInviteModal');
      } else {
        console.log('\n❌ 测试失败：返回内容不包含AiInviteModal');
      }
      
      if (text.includes('找到 1 个相关文档') || text.includes('找到') && text.includes('文档')) {
        console.log('✅ 测试成功：返回内容表明找到了文档');
      } else {
        console.log('❌ 测试失败：返回内容表明未找到文档');
      }
    } else {
      console.log('❌ 测试失败：返回结果格式不正确');
      console.log('实际返回:', JSON.stringify(result, null, 2));
    }
    
  } catch (error) {
    console.error('💥 测试异常:', error);
  }
}

testMcpToolDirect().catch(console.error);
