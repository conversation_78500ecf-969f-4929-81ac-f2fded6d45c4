针对服务器开发者
开始构建您自己的服务器，供 Claude for Desktop 和其他客户端使用。
在本教程中，我们将构建一个简单的 MCP 天气服务，并将其连接到 Claude for Desktop 。基础设置开始，逐步实现更复杂的用例。

我们将构建什么
许多 LLM（包括 Claude）目前无法获取天气预报和恶劣天气警报。让我们使用 MCP 来解决这个问题！

我们将构建一个服务器，该服务器暴露两个工具：get-alerts 和 get-forecast。然后，我们将服务器连接到 MCP 主机（在本案例中是 Claude for Desktop）：

weather-alert

weather-current

服务器可以连接到任何客户端。这里我们选择了 Claude for Desktop 来保持简单，但我们还提供了如何构建您自己的客户端指南以及其他客户端列表。

“为什么选择 Claude for Desktop 而不是 Claude.ai？” 因为服务器是本地运行的，所以 MCP 目前仅支持桌面主机。远程主机正在积极开发中。

MCP 的核心概念
MCP 服务器可以提供三种主要类型的功能：

资源：客户端可以读取的类似文件的数据（如 API 响应或文件内容）
工具：LLM 可调用的函数（需用户批准）
提示：帮助用户完成特定任务的预写模板
本教程将主要关注工具。

Python
Node
Java
Kotlin
让我们从构建天气服务开始，接下来要构建的全部代码保存在 weather-server-python 项目中。

必备知识
本快速入门假定您熟悉以下内容：

Python
像 Claude 这样的大型语言模型（LLM）
系统要求
已安装 Python 3.10 或更高版本。
您必须使用 Python MCP SDK 1.2.0 或更高版本。
环境设置
首先安装 uv 并设置 Python 项目和环境：

Linux/MacOS
curl -LsSf https://astral.sh/uv/install.sh | sh
Windows
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
之后，请确保重启您的终端，以确保 uv 命令能够被识别。

现在，让我们创建并设置我们的项目：

Linux/MacOS
# Create a new directory for our project
uv init weather
cd weather

# Create virtual environment and activate it
uv venv
source .venv/bin/activate

# Install dependencies
uv add "mcp[cli]" httpx

# Create our server file
touch weather.py
Windows
# Create a new directory for our project
uv init weather
cd weather

# Create virtual environment and activate it
uv venv
.venv\Scripts\activate

# Install dependencies
uv add mcp[cli] httpx

# Create our server file
new-item weather.py
下一步，我们开始构建我们的服务器

构建您的服务器
导入包并设置实例
将以下内容添加到您的 weather.py 文件顶部：

from typing import Any
import httpx
from mcp.server.fastmcp import FastMCP

# Initialize FastMCP server
mcp = FastMCP("weather")

# Constants
NWS_API_BASE = "https://api.weather.gov"
USER_AGENT = "weather-app/1.0"
FastMCP 类使用 Python 类型提示和文档字符串自动生成工具定义，使得创建和维护 MCP 工具变得简单。

辅助函数
接下来，让我们添加一个辅助函数，查询天气服务 API 并对结果数据进行格式化：

async def make_nws_request(url: str) -> dict[str, Any] | None:
    """Make a request to the NWS API with proper error handling."""
    headers = {
        "User-Agent": USER_AGENT,
        "Accept": "application/geo+json"
    }
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, headers=headers, timeout=30.0)
            response.raise_for_status()
            return response.json()
        except Exception:
            return None

def format_alert(feature: dict) -> str:
    """Format an alert feature into a readable string."""
    props = feature["properties"]
    return f"""
Event: {props.get('event', 'Unknown')}
Area: {props.get('areaDesc', 'Unknown')}
Severity: {props.get('severity', 'Unknown')}
Description: {props.get('description', 'No description available')}
Instructions: {props.get('instruction', 'No specific instructions provided')}
"""
工具实现
加入工具的逻辑实现：

@mcp.tool()
async def get_alerts(state: str) -> str:
    """Get weather alerts for a US state.

    Args:
        state: Two-letter US state code (e.g. CA, NY)
    """
    url = f"{NWS_API_BASE}/alerts/active/area/{state}"
    data = await make_nws_request(url)

    if not data or "features" not in data:
        return "Unable to fetch alerts or no alerts found."

    if not data["features"]:
        return "No active alerts for this state."

    alerts = [format_alert(feature) for feature in data["features"]]
    return "\n---\n".join(alerts)

@mcp.tool()
async def get_forecast(latitude: float, longitude: float) -> str:
    """Get weather forecast for a location.

    Args:
        latitude: Latitude of the location
        longitude: Longitude of the location
    """
    # First get the forecast grid endpoint
    points_url = f"{NWS_API_BASE}/points/{latitude},{longitude}"
    points_data = await make_nws_request(points_url)

    if not points_data:
        return "Unable to fetch forecast data for this location."

    # Get the forecast URL from the points response
    forecast_url = points_data["properties"]["forecast"]
    forecast_data = await make_nws_request(forecast_url)

    if not forecast_data:
        return "Unable to fetch detailed forecast."

    # Format the periods into a readable forecast
    periods = forecast_data["properties"]["periods"]
    forecasts = []
    for period in periods[:5]:  # Only show next 5 periods
        forecast = f"""
{period['name']}:
Temperature: {period['temperature']}°{period['temperatureUnit']}
Wind: {period['windSpeed']} {period['windDirection']}
Forecast: {period['detailedForecast']}
"""
        forecasts.append(forecast)

    return "\n---\n".join(forecasts)
启动服务
最后，让我们初始化并运行服务器：

if __name__ == "__main__":
    # Initialize and run the server
    mcp.run(transport='stdio')
您的服务器已完成！运行 uv run weather.py 以确认一切正常运行。

现在让我们从现有的 MCP 主机 Claude for Desktop 测试您的服务器。

使用 Claude for Desktop 测试您的服务器
Claude for Desktop 目前在 Linux 上尚不可用。Linux 用户可以继续进行 构建客户端 教程，以构建连接到我们刚刚构建的服务器的 MCP 客户端。

首先，确保您已安装 Claude for Desktop。您可以安装最新版本。 如果您已经安装了 Claude for Desktop，请确保已更新到最新版本。

我们需要为 Claude for Desktop 配置您想要使用的 MCP 服务器。为此，请在文本编辑器中打开您的 Claude for Desktop 应用程序配置，位于 ~/Library/Application Support/Claude/claude_desktop_config.json。如果文件不存在，请确保创建它。

例如使用 VS Code：

Linux/MacOS：code ~/Library/Application\ Support/Claude/claude_desktop_config.json
Windows：code $env:AppData\Claude\claude_desktop_config.json
然后在 mcpServers 键中添加您的服务器。至少要配置一个服务器，MCP UI 元素才会显示在 Claude for Desktop 中。

添加一个天气服务器的定义：

{
    "mcpServers": {
        "weather": {
            "command": "uv",
            "args": [
                "--directory",
                "/ABSOLUTE/PATH/TO/PARENT/FOLDER/weather",
                "run",
                "weather.py"
            ]
        }
    }
}
您可能需要在 command 字段中输入 uv 可执行文件的完整路径。您可以通过在 MacOS/Linux 上运行 which uv 或在 Windows 上运行 where uv 来获取此路径。 请确保传入服务器的绝对路径。

这将告诉 Claude for Desktop：

有一个名为 weather 的 MCP 服务器；
通过运行 uv --directory /ABSOLUTE/PATH/TO/PARENT/FOLDER/weather run weather.py 来启动它。
保存文件，然后重启 Claude for Desktop。

使用命令测试
让我们确保 Claude for Desktop 能够识别我们在 weather 服务器中暴露的两个工具。您可以通过查找锤子图标来确认：

tools

点击锤子图标后，您应该能看到列出的两个工具：

2tools

如果您的服务器未被 Claude for Desktop 识别，请转到故障排除 部分查看调试建议。

如果锤子图标已显示，您现在可以通过在 Claude for Desktop 中运行以下命令来测试您的服务器：

萨克拉门托的天气如何？
德克萨斯州当前的天气警报有哪些？
weather-alert

weather-current

由于这是美国国家气象服务，查询仅适用于美国地点。
幕后发生了什么
当您提出一个问题时：

客户端将您的问题发送给 Claude
Claude 分析可用工具并决定使用哪些工具
客户端通过 MCP 服务器执行所选工具
结果返回给 Claude
Claude 制定自然语言响应
响应显示给您！
故障排除
Claude for Desktop 集成问题
从 Claude for Desktop 获取日志
Claude.app 中与 MCP 相关的日志会写入 ~/Library/Logs/Claude 中的日志文件：

mcp.log 包含关于 MCP 连接和连接失败的一般日志。
以 mcp-server-SERVERNAME.log 命名的文件包含来自指定服务器的错误（stderr）日志。
您可以运行以下命令列出最近的日志并跟踪新日志：

# 检查 Claude 的日志以查找错误
tail -n 20 -f ~/Library/Logs/Claude/mcp*.log
服务器未在 Claude 中显示
检查您的 claude_desktop_config.json 文件语法
确保项目路径是绝对路径而非相对路径
完全重启 Claude for Desktop
工具调用无声失败
如果 Claude 尝试使用工具但失败：

检查 Claude 的日志以查找错误
验证您的服务器能够构建并运行无误
尝试重启 Claude for Desktop
这些都不起作用。我该怎么办？
请参阅我们的调试指南，获取更好的调试工具和更详细的指导。

错误：无法检索地点天气数据
这通常意味着：

坐标位于美国以外
NWS API 出现问题
您被限制了请求速率
解决方法：

验证您使用的是美国坐标
在请求之间添加短暂延迟
检查 NWS API 状态页面
错误：没有 [某个州] 的活动警报
这不是错误——只是表示该州当前没有天气警报。尝试其他州或在恶劣天气时检查。

