// 测试路径检测逻辑
import fs from 'fs';
import path from 'path';

function testPathDetection() {
  console.log('=== 🔍 路径检测测试 ===\n');
  
  const cwd = process.cwd();
  console.log(`当前工作目录: ${cwd}`);
  
  // 检测逻辑
  const isDistExecution = cwd.includes('node_modules') || fs.existsSync(path.join(cwd, 'dist', 'docs'));
  
  console.log(`是否为dist执行: ${isDistExecution}`);
  console.log(`工作目录包含node_modules: ${cwd.includes('node_modules')}`);
  console.log(`dist/docs存在: ${fs.existsSync(path.join(cwd, 'dist', 'docs'))}`);
  
  // 计算docs基础路径
  const docsBasePath = isDistExecution ? path.join(cwd, 'dist', 'docs') : path.join(cwd, 'src', 'docs');
  
  console.log(`\n文档基础路径: ${docsBasePath}`);
  console.log(`基础路径存在: ${fs.existsSync(docsBasePath)}`);
  
  // 测试npm包路径
  const packageName = '@nibfe/crm-pc-react-components';
  const localPath = packageName.split('/')[1]; // crm-pc-react-components
  const docsPath = path.join(docsBasePath, localPath);
  
  console.log(`\nNPM包名: ${packageName}`);
  console.log(`转换后本地路径: ${localPath}`);
  console.log(`完整文档路径: ${docsPath}`);
  console.log(`文档路径存在: ${fs.existsSync(docsPath)}`);
  
  if (fs.existsSync(docsPath)) {
    console.log('\n📁 目录内容:');
    const files = fs.readdirSync(docsPath);
    files.forEach(file => {
      console.log(`  - ${file}`);
    });
    
    // 检查AiInviteModal.md
    const targetFile = path.join(docsPath, 'AiInviteModal.md');
    console.log(`\n🎯 查找 AiInviteModal.md:`);
    console.log(`  路径: ${targetFile}`);
    console.log(`  存在: ${fs.existsSync(targetFile)}`);
    
    if (fs.existsSync(targetFile)) {
      const content = fs.readFileSync(targetFile, 'utf-8');
      console.log(`  文件大小: ${content.length} 字符`);
      console.log(`  前50个字符: ${content.substring(0, 50)}...`);
    }
  } else {
    console.log(`\n❌ 文档路径不存在: ${docsPath}`);
    
    // 列出可能的路径
    console.log('\n🔍 检查可能的路径:');
    [
      path.join(cwd, 'src', 'docs', localPath),
      path.join(cwd, 'dist', 'docs', localPath),
      path.join(cwd, 'docs', localPath)
    ].forEach(testPath => {
      console.log(`  ${testPath}: ${fs.existsSync(testPath) ? '✅' : '❌'}`);
    });
  }
}

testPathDetection();
