#!/usr/bin/env node

/**
 * 测试MCP客户端 - 验证修复后的外部文档查询功能
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn } from 'child_process';

async function testMCPClient() {
  console.log('🧪 开始测试MCP客户端功能\n');

  // 启动MCP服务器进程
  const serverProcess = spawn('node', ['dist/index.js'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    cwd: process.cwd()
  });

  // 创建客户端传输
  const transport = new StdioClientTransport({
    reader: serverProcess.stdout,
    writer: serverProcess.stdin
  });

  // 创建客户端
  const client = new Client(
    {
      name: 'test-client',
      version: '1.0.0'
    },
    {
      capabilities: {}
    }
  );

  try {
    // 连接到服务器
    await client.connect(transport);
    console.log('✅ 成功连接到MCP服务器');

    // 列出可用工具
    const tools = await client.listTools();
    console.log('\n📋 可用工具:');
    tools.tools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description.substring(0, 100)}...`);
    });

    // 测试外部文档查询工具
    console.log('\n🔍 测试外部文档查询工具...');
    
    const testCases = [
      {
        name: '测试查询@nibfe/crm-pc-react-components包',
        params: {
          componentNames: ['AiInviteModal'],
          sources: ['@nibfe/crm-pc-react-components'],
          limit: 5
        }
      },
      {
        name: '测试查询不存在的包',
        params: {
          componentNames: ['TestComponent'],
          sources: ['@test/non-existent'],
          limit: 5
        }
      }
    ];

    for (const testCase of testCases) {
      console.log(`\n📝 ${testCase.name}`);
      try {
        const result = await client.callTool({
          name: 'search_external_docs',
          arguments: testCase.params
        });

        console.log('✅ 调用成功');
        console.log('📊 结果:');
        if (result.content && result.content[0] && result.content[0].text) {
          const text = result.content[0].text;
          // 只显示前500个字符
          console.log(text.substring(0, 500) + (text.length > 500 ? '...' : ''));
        } else {
          console.log(JSON.stringify(result, null, 2));
        }
      } catch (error) {
        console.error('❌ 调用失败:', error.message);
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    // 清理资源
    try {
      await client.close();
      serverProcess.kill();
      console.log('\n🧹 清理完成');
    } catch (error) {
      console.error('清理时出错:', error.message);
    }
  }
}

// 运行测试
testMCPClient().catch(console.error);
