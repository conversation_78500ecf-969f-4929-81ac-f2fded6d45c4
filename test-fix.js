#!/usr/bin/env node

/**
 * 测试修复后的文档路径检测功能
 */

import fs from 'fs';
import path from 'path';

// 创建一个简化的测试类来模拟ExtDocsApiClient的关键方法
class TestExtDocsApiClient {
  constructor(projectRoot = process.cwd()) {
    this.projectRoot = projectRoot;
  }

  /**
   * 将npm包名转换为本地文档路径
   */
  npmPackageToLocalPath(packageName) {
    if (packageName.startsWith('@')) {
      const parts = packageName.split('/');
      if (parts.length === 2) {
        return parts[1];
      }
    }
    return packageName;
  }

  /**
   * 获取文档基础路径
   */
  getDocsBasePath() {
    const isDistExecution =
      process.cwd().includes('node_modules') ||
      fs.existsSync(path.join(process.cwd(), 'dist', 'docs'));
    
    const docsBasePath = isDistExecution
      ? path.join(process.cwd(), 'dist', 'docs')
      : path.join(this.projectRoot, 'src', 'docs');
    
    console.log(`📍 文档基础路径检测:`);
    console.log(`   - isDistExecution: ${isDistExecution}`);
    console.log(`   - docsBasePath: ${docsBasePath}`);
    console.log(`   - 路径存在: ${fs.existsSync(docsBasePath)}`);
    
    return docsBasePath;
  }

  /**
   * 测试搜索本地npm包文档
   */
  async testSearchLocalNpmDocs(packageName) {
    console.log(`\n🔍 测试搜索本地npm包文档: ${packageName}`);
    
    try {
      const localPath = this.npmPackageToLocalPath(packageName);
      const docsBasePath = this.getDocsBasePath();
      const docsPath = path.join(docsBasePath, localPath);

      console.log(`   - 包名: ${packageName}`);
      console.log(`   - 本地路径: ${localPath}`);
      console.log(`   - 完整文档路径: ${docsPath}`);
      console.log(`   - 路径存在: ${fs.existsSync(docsPath)}`);

      if (fs.existsSync(docsPath)) {
        const files = fs.readdirSync(docsPath);
        console.log(`   - 文件数量: ${files.length}`);
        console.log(`   - 文件列表: ${files.slice(0, 5).join(', ')}${files.length > 5 ? '...' : ''}`);
        
        return {
          success: true,
          data: files.map(file => ({
            id: `local-${packageName}-${file}`,
            title: file.replace('.md', ''),
            content: `文档内容: ${file}`,
            source: `本地文档 (${packageName})`,
            type: 'component'
          })),
          total: files.length,
          message: `从本地文档找到 ${files.length} 个相关文档`
        };
      } else {
        return {
          success: false,
          data: [],
          total: 0,
          message: `本地文档路径不存在: ${localPath}    ${docsPath}`
        };
      }
    } catch (error) {
      console.error(`❌ 测试失败:`, error.message);
      return {
        success: false,
        data: [],
        total: 0,
        message: `搜索本地文档时发生错误: ${error.message}`
      };
    }
  }
}

async function runTest() {
  console.log('🧪 开始测试修复后的文档路径检测功能\n');
  
  const client = new TestExtDocsApiClient();
  
  // 测试1: 测试@nibfe/crm-pc-react-components包
  const result1 = await client.testSearchLocalNpmDocs('@nibfe/crm-pc-react-components');
  console.log('\n📊 测试结果1:');
  console.log(JSON.stringify(result1, null, 2));
  
  // 测试2: 测试不存在的包
  const result2 = await client.testSearchLocalNpmDocs('@test/non-existent-package');
  console.log('\n📊 测试结果2:');
  console.log(JSON.stringify(result2, null, 2));
  
  console.log('\n✅ 测试完成');
}

runTest().catch(console.error);
