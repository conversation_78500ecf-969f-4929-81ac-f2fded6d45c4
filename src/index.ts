import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { getServerConfig } from "./config.js";
import { aiFriendlyDocsMCPServer } from "./server.js";

async function main() {
  const config = getServerConfig(false); // 显示配置信息
  
  try {
    const server = await aiFriendlyDocsMCPServer(config);
  const transport = new StdioServerTransport();
  await server.connect(transport);
    
    console.error("AI友好文档查询MCP服务器已启动");
    if (config.sources.length > 0) {
      console.error(`配置的文档源: ${config.sources.join(', ')}`);
    } else {
      console.error("未配置文档源，可使用 --source 参数指定");
    }
  } catch (error) {
    console.error("启动服务器时发生错误:", error);
    process.exit(1);
  }
}

main().catch((error) => {
  console.error("启动失败:", error);
  process.exit(1);
});
