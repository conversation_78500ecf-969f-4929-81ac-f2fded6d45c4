# 1. 作用
主要介绍到店研发的架构开发规范，主要是DDD架构，本文介绍了DDD各层的含义、职责、规范等。

# 2. 架构分层规范
本文中的 `*-api`、`*-starter`、`*-application`、`*-domain`、`*-infrastructure` 为示例命名，实际Module所在目录，可在当前项目中找到！

分层规范：

| Module            | 含义         | 职责描述和规范说明                                                                                           |
|-------------------|--------------|-------------------------------------------------------------------------------------------------------------|
| `*-api`           | API模块      | 负责定义接口和模型，包括各类协议的模型，通常以Request、Response、DTO等结尾。<br>`API模块命名后缀为api`。若有团队惯例，也可使用诸如client等命名风格。 |
| `*-starter`       | 接入层       | 负责项目启动配置和业务入口，前者包括服务配置、切面等技术能力，后者包括接口协议适配、基础参数校验、请求转发、响应结果封装、异常处理、日志打点等工作。<br>`启动模块命名后缀为starter`。 |
| `*-application`   | 应用层       | 负责用例实现，通常串联内部领域服务接口或外部服务接口，实现流程编排、聚合查询等工作流操作。<br>`应用层模块命名后缀为application`。应用层模块可选，通常在组合编排场景用例占比较高时使用。 |
| `*-domain`        | 领域层       | 负责业务逻辑实现，通常抽象出领域对象，来表达业务概念，承载业务行为和规则。<br>`领域层模块命名后缀为domain`。领域层模块可选，通常在有内部的领域实体、下沉通用逻辑等情况下使用。 |
| `*-infrastructure`| 基础设施层   | 通常负责外部调用，比如数据库的CRUD、搜索引擎、文件系统、分布式服务的RPC等。<br>`基础设施层模块命名后缀为infrastructure`。 |

每层的具体作用：实际开发中，会按需定义这些目录。Package会在指定的路径之后创建，如 `com.meituan.b2c`

| Module            | Package     | 含义         | 职责描述和规范说明                                                                                           |
|-------------------|-------------|--------------|-------------------------------------------------------------------------------------------------------------|
| `*-api`           | `dto`       | 传输对象     | API接口模型中的传输对象。                                                                                   |
| `*-api`           | `request`   | 请求对象     | API接口模型中的请求参数对象。                                                                               |
| `*-api`           | `response`  | 返回对象     | API接口模型中的返回结果对象。MThrift需要定义特定Respone类，如CreateOrderIdResponse。                          |
| `*-api`           | `service`   | 服务接口     | 通常为MThrift、Pigeon服务接口。                                                                             |
| `*-starter`       | ---         | ---          | 启动器类ApplicationLoader。                                                                                 |
| `*-starter`       | `aop`       | 切面类       | 切面类。                                                                                                    |
| `*-starter`       | `config`    | 配置类       | 服务配置信息。                                                                                              |
| `*-starter`       | `consumer`  | 消费者       | Mafka消息的消费者。                                                                                         |
| `*-starter`       | `controller`| 控制器       | 指HTTP接口。                                                                                                |
| `*-starter`       | `gateway`   | 网关接口实现 | 使用Shepherd（牧羊人）、Aegis（商家）等网关，对应的RPC接口。                                                 |
| `*-starter`       | `mapi`      | 移动端接口   | 移动端接口。                                                                                                |
| `*-starter`       | `job`       | 定时任务     | 通常为Crane定时任务。                                                                                       |
| `*-starter`       | `service`   | 服务接口实现 | 通常为MThrift、Pigeon服务接口实现。                                                                         |
| `*-application`   | `model`     | 模型         | 应用层接口模型中的请求对象和返回对象。                                                                       |
| `*-application`   | `service`   | 应用服务     | 负责串联内部领域服务接口或外部资源接口，实现组合编排控制。需定义接口和实现类。                                 |
| `*-domain`        | `ability`   | 领域对象行为 | 领域对象的行为，以类的形式存在。在复杂系统中，领域对象行为需要被多处复用，大多实践中，会抽象出独立的行为类。DDD实践中，通常建议优先将行为分配至领域对象类中。 |
| `*-domain`        | `constant`  | 常量         | 常量                                                                                                        |
| `*-domain`        | `enums`     | 枚举         | 枚举                                                                                                        |
| `*-domain`        | `model`     | 模型         | 领域层接口模型中的请求对象和返回对象，以及领域对象。                                                         |
| `*-domain`        | `service`   | 领域服务     | 负责业务逻辑的实现，通常协调多个领域对象，仓储服务或外部资源接口。                                           |
| `*-infrastructure`| `dal`       | 数据访问层   | 负责对数据库的访问操作，也称为数据访问层。                                                                   |
| `*-infrastructure`| `proxy`     | 外部服务代理 | 负责对外部服务的调用封装。                                                                                   |
| `*-infrastructure`| `cache`     | 缓存         | 负责对缓存客户端的封装。                                                                                     |
| `*-infrastructure`| `config`    | 业务配置     | 负责业务相关的控制、参数等配置信息。                                                                         |
| `*-infrastructure`| `producer`  | 消息发布     | 负责消息的发送。                                                                                            |
| `*-infrastructure`| `...`       |              | 其他外部资源类型，如doris等，不再一一列举。                                                                  |
