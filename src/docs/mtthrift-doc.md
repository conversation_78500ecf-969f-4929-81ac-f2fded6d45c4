# 1. 作用
`mtthrift`是美团自研的RPC框架，用于实现分布式系统之间的通信和调用。本文主要介绍`mtthrift`框架的依赖、相关配置和使用方法。

# 2. 相关依赖和配置

## 2.1 pom依赖
以下为本文档所有的依赖，需要根据使用按需引入。

```xml
<!--mtthrift框架依赖-->
<dependency>
    <groupId>com.meituan.service.mobile</groupId>
    <artifactId>mtthrift</artifactId>
    <version>2.11.1</version>
</dependency>

<!--lombok框架依赖-->
<dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
    <version>1.18.34</version>
</dependency>

<!--mdp框架依赖-->
<dependency>
    <groupId>com.meituan.mdp.boot</groupId>
    <artifactId>mdp-boot-starter-thrift</artifactId>
</dependency>

## 2.2 服务端定义

### 2.2.1 接口定义
- **要求**：
  - 接口类上添加`@ThriftService`注解
  - 接口方法上添加`@ThriftMethod`注解
  - 方法定义一定要抛出`TException`异常

- **案例如下**：

```java
import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import org.apache.thrift.TException;

@ThriftService
public interface TestService {
    @ThriftMethod
    public TestResponse method1(TestRequest testRequest) throws TException;
}

### 2.2.2 参数定义

#### 方法的出参和入参
- **要求**：
  - 需要在出入参上增加如下注释
  - 在类上增加`mtthrift`依赖的`@ThriftStruct`，`lombok`依赖的`@Getter`、`@Setter`、`@ToString`、`@EqualsAndHashCode`
  - 在字段属性上增加`@ThriftField(value = 1)`注解，`value`需要自增

- **案例如下**：

```java
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ThriftStruct
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class TestRequest {
    @ThriftField(value = 1)
    private Integer userid;
    
    @ThriftField(value = 2)
    private String message;
    
    @ThriftField(value = 3)
    private Integer seqid;
}

#### 枚举
符合Java8的枚举定义即可。

```java
public enum TestEnum {
    SUCCESS(0, "成功"),
    FAIL(1, "失败");

    private final int code;
    private final String desc;

    TestEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}

### 2.2.3 实现类
- **要求**：
  - 类上增加`@MdpThriftServer`和`@MdpThriftServerExtConfig`注解

- **案例如下**：

```java
import lombok.extern.slf4j.Slf4j;

@Slf4j
@MdpThriftServer(
    port = 9001
)
@MdpThriftServerExtConfig(
    enableAuthHandler = true
)
public class TestServiceImpl implements TestService {
    @Override
    public TestResponse method1(TestRequest testRequest) throws TException {
        // 具体实现
    }
}

## 2.3 服务端注入
- **要求**：
  - 在类上引入Spring的`@Configuration`注解，表示这是一个配置类
  - 在类上引入Mdp的`@MdpThriftClient`注解，表示这是一个`mtthrift`客户端，参数包括`remoteAppKey`-远程服务`appkey`、`timeout`-超时时间

```java
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PigeonConfig {
    @MdpThriftClient(
        remoteAppKey = "com.meituan.helloService",
        timeout = 2000,
        remoteServerPort = 9090,
        testTimeout = 3000
    )
    private HelloService.Iface helloSerivce;
}

# MtThrift 服务端和客户端接入文档

## MTthrift依赖

针对maven项目，通过dependencyManagement导入inf-bom，并指定MTthrift依赖
<dependencyManagement>
	<dependencies>
    	<dependency>
        	<groupId>com.sankuai</groupId>
            <artifactId>inf-bom</artifactId>
            <version>..*</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
	</dependencies>
</dependencyManagement>
  
<dependencies>
	<dependency>
		<groupId>com.meituan.service.mobile</groupId>
		<artifactId>mtthrift</artifactId>
	</dependency>
</dependencies>


## MTthrift 接入流程示例

定义接口文件

下面以hello.thrift文件为例，IDL语言细节具体参考 Thrift IDL 开发规范

hello.thrift

namespace java com.meituan.mtthrift.test
service HelloService
{   
    string sayHello(1:string username)
    string sayBye(1:string username)
}

生成代码

根据hello.thrift生成Java代码

使用Genthrift（https://octo.mws.sankuai.com/compile-online），只需添加目标文件（hello.thrift），点击提交按钮即可，生成的目标代码自动下载到本地

java工程请使用maven的dolphin插件，https://km.sankuai.com/page/28379894（推荐）

申请appkey

appkey是美团内部作为应用的唯一标识。1个应用对应1个appkey。1个appkey下有多个机器，每台机器上部署服务端或客户端。

已统一到Avatar申请，不区分环境

申请地址：https://avatar.mws.sankuai.com/#/home , 选择  申请新服务

以线下环境中的appkey：com.sankuai.inf.mtthrift.testServer 和 com.sankuai.inf.mtthrift.testClient 为例，后面请注意我们在哪里使用了它们 

强烈建议自己申请appkey（server端和client端）不要用wiki中的，用wiki中的可能会拿到错的服务机器列表，因为很多人都在使用该appkey并发布了自己的服务

服务端发布

(1)配置server.xml文件（可参考MTthriftServerDemo中的server.xml，已经自动配置，注意appKey的值） 

server.xml

<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

    <bean id="serviceProcessor" class="com.meituan.mtthrift.test.HelloServiceImpl"></bean>

    <bean id="服务端bean ID" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.mtthrift.test.HelloService"/> <!-- 接口类 -->
        <property name="serviceImpl" ref="serviceProcessor"/> <!-- 实现类 -->
        <property name="appKey" value="com.sankuai.inf.mtthrift.testServer"/>  <!-- 本地appkey -->
        <property name="port" value="9001"/> <!-- 监听端口； >=1.8.6.5可不配，注意事项见: https://km.sankuai.com/page/163558219 -->
    </bean>

</beans>


<bean id="helloServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
<property name="serviceImpl">
    <bean class="com.meituan.service.mobile.mtthrift.HelloServiceImpl" /> <!-- service实现类 -->
</property>    
</bean>

<bean id="echoServiceBean" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServiceBean">
    <property name="serviceImpl">
    <bean class="com.meituan.service.mobile.mtthrift.EchoServiceImpl" /> <!-- service实现类 -->
</property>    
</bean>
 
<bean id="multiSerivcePublisher" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher"
      init-method="publish" destroy-method="destroy">
    <property name="appKey" value="com.sankuai.inf.mtthrift.testServer"/>
    <property name="port" value="9001"/>
    <property name="serviceProcessorMap"> <!-- 只需配置serviceProcessorMap -->
        <map>
            <entry key="com.meituan.service.mobile.mtthrift.HelloService" value-ref="helloServiceBean"/>
            <entry key="com.meituan.service.mobile.mtthrift.EchoService" value-ref="echoServiceBean"/>
        </map>
    </property>
</bean>


（2）实现Service接口（可参考MTthriftServerDemo中的HelloServiceImpl类，下面仅供参考）

import org.apache.thrift.TException;

public class HelloServiceImpl implements HelloService.Iface {

    public String sayHello(String username) throws TException {
        return "hello, " + username;
    }

    public String sayBye(String username) throws TException {
        return "bye, " + username;
    }

}

（3）启动Server端服务（运行MTthriftServerDemo中的Server类即可）

客户端

（1）配置client.xml文件（可参考MTthriftClientDemo中的client.xml，已经自动配置，注意remoteAppkey的值）

client.xml

<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="客户端bean ID" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.mtthrift.test.HelloService"/> <!-- 接口名 -->
        <property name="remoteAppkey" value="com.sankuai.inf.mtthrift.testServer"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
    </bean>

</beans>

（2）实现调用（可参考MTthriftClientDemo中的client类，下面仅供参考）

import org.apache.thrift.TException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.support.ClassPathXmlApplicationContext;

public class Client {

    private static HelloService.Iface client;

    public static void main(String[] args) throws InterruptedException {
        BeanFactory beanFactory = new ClassPathXmlApplicationContext("client.xml");
        client = (HelloService.Iface) beanFactory.getBean("clientProxy");
        
        Thread.sleep(3000);

        try {
            String result = client.sayHello("meituan");
            System.out.println("\n" + result + "\n");
        } catch (TException e) {
            e.printStackTrace();
        }
        System.exit(0);
    }
}

（3）启动Client端服务（运行MTthriftClientDemo中的Client类即可）

（4）控制台输出"hello, meituan"（确保Server端已经运行），说明RPC调用成功






