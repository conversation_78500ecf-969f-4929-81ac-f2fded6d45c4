{"name": "项目文档", "docs": [{"title": "@nibfe/crm-pc-react-components", "type": "component", "desc": "这是@nibfe/crm-pc-react-components的组件库，可以按组件名批量查阅组件API和开发示例", "summary": "docs/crm-pc-react-components/README.md", "items": [{"title": "AddTaskModal", "path": "docs/crm-pc-react-components/add-task-modal.md"}, {"title": "AiInviteModal", "path": "docs/crm-pc-react-components/ai-invite-modal.md"}, {"title": "AudioPlayer", "path": "docs/crm-pc-react-components/audio-player.md"}, {"title": "CallRecord", "path": "docs/crm-pc-react-components/call-record.md"}, {"title": "CreatePlanModal", "path": "docs/crm-pc-react-components/create-plan-modal.md"}, {"title": "ChatRecord", "path": "docs/crm-pc-react-components/chat-record.md"}]}, {"title": "项目公共组件", "type": "component", "desc": "这是项目的定义公共组件，可以按组件名批量查阅组件API和开发示例", "summary": "", "items": []}, {"title": "工具", "type": "utils", "desc": "这是项目的定义工具类，可以通过工具名批量查阅可用的工具方法", "items": []}], "metadata": {"author": "", "techStack": "React 17.x, TypeScript, SCSS, MTD React", "repository": "", "description": "apollo-shop-pc-static-react - 基于 @nibfe/crm-pc-react-components@0.0.7 的CRM PC端React组件库", "version": "0.1.0"}}