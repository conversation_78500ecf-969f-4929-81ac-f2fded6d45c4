---
title: "CreatePlanModal"
description: "一个用于创建计划的弹窗组件，支持单个或批量店铺操作。"
---
## 功能特性
`CreatePlanModal` 是一个通过 `ref` 调用的弹窗组件，用于创建计划。它支持传入单个或多个店铺信息，并提供计划创建成功和关闭时的回调。

## 调用方式
```tsx
import CreatePlanModal, { CreatePlanModalHandle } from '@nibfe/crm-pc-react-components/dist/components/create-plan-modal';
```

## 属性 (Props)
| 属性名            | 类型           | 是否必选 | 默认值 | 描述                     |
| ----------------- | -------------- | -------- | ------ | ------------------------ |
| `destroyOnClose`  | `boolean`      | 否       | -      | 关闭时是否销毁弹窗       |
| `onModalClose`    | `() => void`   | 否       | -      | 弹窗关闭时的回调         |
| `onSuccess`       | `() => void`   | 否       | -      | 创建计划成功时的回调     |

## 方法 (Handle)
| 方法名  | 类型                                | 描述                                                         |
| ------- | ----------------------------------- | ------------------------------------------------------------ |
| `open`  | `(params: OpenParams) => void`      | 打开弹窗。`params` 包含计划ID、店铺数据和是否允许更改店铺等配置。 |

### open(params: OpenParams)
`OpenParams` 对象的属性如下：

| 属性名          | 类型                      | 是否必选 | 描述                           |
| --------------- | ------------------------- | -------- | ------------------------------ |
| `id`            | `number \| string`        | 否       | 计划ID（编辑时使用）。         |
| `shopData`      | `ShopInfo[] \| ShopInfo`  | 否       | 单个或多个店铺信息。           |
| `canChangeShop` | `boolean`                 | 否       | 是否允许更改店铺。             |

### ShopInfo
店铺信息对象的结构：
| 属性名     | 类型     | 是否必选 | 描述      |
| ---------- | -------- | -------- | --------- |
| `shopId`   | `number` | 是       | 店铺ID。  |
| `shopName` | `string` | 是       | 店铺名称。|

## 代码示例
```tsx
import React, { useRef } from 'react';
import CreatePlanModal, { CreatePlanModalHandle } from '@nibfe/crm-pc-react-components/dist/components/create-plan-modal';

const MyComponent = () => {
  const modalRef = useRef<CreatePlanModalHandle>(null);

  const handleOpenModal = () => {
    modalRef.current?.open({
      shopData: { shopId: 123, shopName: '示例店铺' },
      canChangeShop: true,
    });
  };

  const handleEditPlan = () => {
    modalRef.current?.open({
      id: 456,
      shopData: [
        { shopId: 123, shopName: '店铺一' },
        { shopId: 124, shopName: '店铺二' },
      ],
      canChangeShop: false,
    });
  };

  return (
    <>
      <button onClick={handleOpenModal}>创建新计划</button>
      <button onClick={handleEditPlan}>编辑计划</button>
      <CreatePlanModal
        ref={modalRef}
        destroyOnClose={true}
        onSuccess={() => {
          console.log('计划创建/编辑成功');
        }}
        onModalClose={() => {
          console.log('弹窗已关闭');
        }}
      />
    </>
  );
};

export default MyComponent;
``` 