---
title: "CallRecord"
description: "一个用于展示通话记录列表的组件。"
---
## 功能特性
`CallRecord` 组件用于展示通话记录，通常与音频播放器配合使用。它支持自定义渲染列表项，并提供丰富的回调事件。

## 调用方式
```tsx
import CallRecord from '@nibfe/crm-pc-react-components/dist/components/call-record';
```

## 属性 (Props)
| 属性名             | 类型                                                     | 是否必选 | 描述                           |
| ------------------ | -------------------------------------------------------- | -------- | ------------------------------ |
| `value`            | `number`                                                 | 否       | 当前选中的记录的时间。         |
| `receiverName`     | `string`                                                 | 否       | 接听方姓名。                   |
| `audioAddress`     | `string`                                                 | 否       | 关联的音频地址。               |
| `options`          | `CallRecordItem[]`                                       | 否       | 通话记录数据列表。             |
| `isScrollByTime`   | `boolean`                                                | 否       | 是否根据时间滚动。             |
| `isDoubleClick`    | `boolean`                                                | 否       | 是否支持双击。                 |
| `emptyText`        | `string`                                                 | 否       | 列表为空时显示的文本。         |
| `onChange`         | `(value: number) => void`                                | 否       | 选中项变化时的回调。           |
| `onTimeChange`     | `(time: number) => void`                                 | 否       | 时间变化时的回调。             |
| `renderCallItems`  | `(data: CallRecordItem, index: number) => React.ReactNode` | 否       | 自定义渲染通话记录项。         |

### CallRecordItem
`options` 数组中每个对象的结构：
| 属性名       | 类型        | 是否必选 | 描述              |
| ------------ | ----------- | -------- | ----------------- |
| `content`    | `string`    | 是       | 对话内容。        |
| `startTime`  | `number`    | 是       | 开始时间（秒）。  |
| `endTime`    | `number`    | 是       | 结束时间（秒）。  |
| `avatar`     | `string`    | 否       | 头像 URL。        |
| `isReceiver` | `boolean`   | 是       | 是否为接听方。    |


## 代码示例
```tsx
import React, { useState } from 'react';
import CallRecord from '@nibfe/crm-pc-react-components/dist/components/call-record';
import type { CallRecordItem } from '@nibfe/crm-pc-react-components/dist/components/call-record';

const MyComponent = () => {
  const [value, setValue] = useState(0);

  const mockRecords: CallRecordItem[] = [
    { content: '您好，这里是XX公司。', startTime: 0, endTime: 3, isReceiver: false },
    { content: '你好，请问有什么事吗？', startTime: 4, endTime: 7, isReceiver: true },
    { content: '我们最近有一个新的产品推广活动...', startTime: 8, endTime: 15, isReceiver: false },
  ];

  return (
    <div style={{ width: '300px', border: '1px solid #eee' }}>
      <CallRecord
        options={mockRecords}
        receiverName="客户"
        value={value}
        onChange={(newValue) => {
          console.log('Selected record time:', newValue);
          setValue(newValue);
        }}
      />
    </div>
  );
};

export default MyComponent;
``` 