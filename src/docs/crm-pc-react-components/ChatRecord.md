---
title: "ChatRecord"
description: "一个用于展示聊天记录的组件，支持多种消息类型展示。"
---
## 功能特性
`ChatRecord` 组件用于展示聊天记录列表，支持多种消息类型（文本、图片、语音、视频等），并提供滚动事件监听。

## 调用方式
```tsx
import ChatRecord from '@nibfe/crm-pc-react-components/dist/components/chat-record';
import type { ChatRecordsItem } from '@nibfe/crm-pc-react-components/dist/components/chat-record';
```

## 属性 (Props)
| 属性名         | 类型                                                  | 是否必选 | 描述                           |
| -------------- | ----------------------------------------------------- | -------- | ------------------------------ |
| `emptyText`    | `string`                                              | 否       | 列表为空时显示的文本。         |
| `receiverName` | `string`                                              | 否       | 接收方姓名。                   |
| `options`      | `ChatRecordsItem[]`                                   | 否       | 聊天记录数据列表。             |
| `onScroll`     | `(e: React.UIEvent<HTMLDivElement, UIEvent>) => void`| 否       | 滚动事件回调。                 |

## ChatRecordsItem
聊天记录项的数据结构：

| 属性名                | 类型      | 是否必选 | 描述                                                                    |
| --------------------- | --------- | -------- | ----------------------------------------------------------------------- |
| `content`             | `string`  | 否       | 消息内容。                                                              |
| `empName`             | `string`  | 否       | 员工名称。                                                              |
| `sendTime`            | `string`  | 否       | 发送时间。                                                              |
| `direction`           | `number`  | 否       | 方向，1呼入2呼出（1.客户 2.销售）。                                    |
| `playLength`          | `number`  | 否       | 播放时长。                                                              |
| `contentType`         | `number`  | 否       | 消息类型：1-文本，2-图片，3-视频，4-语音，5-文件，6-音频存档，7-表情，8-名片，9-链接，10-小程序，11-在线文档，12-红包，13-互通红包。 |
| `isShowSendTime`      | `boolean` | 否       | 是否展示发送时间。                                                      |
| `customerNickName`    | `string`  | 否       | 用户名称。                                                              |
| `customerRemarkName`  | `string`  | 否       | 用户备注。                                                              |

## 代码示例
```tsx
import React from 'react';
import ChatRecord from '@nibfe/crm-pc-react-components/dist/components/chat-record';
import type { ChatRecordsItem } from '@nibfe/crm-pc-react-components/dist/components/chat-record';

const MyComponent = () => {
  const mockChatRecords: ChatRecordsItem[] = [
    {
      content: '您好，请问有什么可以帮助您的吗？',
      empName: '客服小李',
      sendTime: '2024-01-15 10:30:00',
      direction: 2, // 销售发送
      contentType: 1, // 文本消息
      isShowSendTime: true,
    },
    {
      content: '我想了解一下你们的产品',
      customerNickName: '张三',
      sendTime: '2024-01-15 10:31:00', 
      direction: 1, // 客户发送
      contentType: 1, // 文本消息
      isShowSendTime: true,
    },
    {
      content: '好的，我为您详细介绍一下我们的产品特点...',
      empName: '客服小李',
      sendTime: '2024-01-15 10:32:00',
      direction: 2, // 销售发送
      contentType: 1, // 文本消息
      isShowSendTime: false,
    },
  ];

  const handleScroll = (e: React.UIEvent<HTMLDivElement, UIEvent>) => {
    console.log('聊天记录滚动中...', e.currentTarget.scrollTop);
  };

  return (
    <div style={{ width: '400px', height: '300px', border: '1px solid #eee' }}>
      <ChatRecord
        options={mockChatRecords}
        receiverName="客户"
        emptyText="暂无聊天记录"
        onScroll={handleScroll}
      />
    </div>
  );
};

export default MyComponent;
``` 