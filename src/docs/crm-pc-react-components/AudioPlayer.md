---
title: "AudioPlayer"
description: "一个可控制的音频播放器组件。"
---
## 功能特性
`AudioPlayer` 是一个音频播放器组件，支持通过 `ref` 控制播放进度，并提供时间变化的回调。

## 调用方式
```tsx
import AudioPlayer, { AudioPlayerRef } from '@nibfe/crm-pc-react-components/dist/components/audio-player';
```

## 属性 (Props)
| 属性名          | 类型                   | 是否必选 | 描述                     |
| --------------- | ---------------------- | -------- | ------------------------ |
| `audioAddress`  | `string`               | 是       | 音频文件的 URL。         |
| `onTimeChange`  | `(time: number) => void`| 否       | 播放时间变化时的回调。   |

## 方法 (Ref)
| 方法名           | 类型                  | 描述                 |
| ---------------- | --------------------- | -------------------- |
| `setCurrentTime` | `(time: number) => void` | 设置当前播放时间（秒）。 |

## 代码示例
```tsx
import React, { useRef } from 'react';
import AudioPlayer, { AudioPlayerRef } from '@nibfe/crm-pc-react-components/dist/components/audio-player';

const MyComponent = () => {
  const playerRef = useRef<AudioPlayerRef>(null);

  const handleSeek = () => {
    // 跳转到音频的第 15 秒
    playerRef.current?.setCurrentTime(15);
  };

  return (
    <div>
      <AudioPlayer
        ref={playerRef}
        audioAddress="https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3"
        onTimeChange={(time) => {
          console.log(`当前播放时间: ${time}s`);
        }}
      />
      <button onClick={handleSeek} style={{ marginTop: '10px' }}>
        跳转到 15s
      </button>
    </div>
  );
};

export default MyComponent;
``` 