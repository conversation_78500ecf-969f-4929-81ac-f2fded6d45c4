---
title: "AddTaskModal"
description: "一个用于添加任务的弹窗组件，支持单个或批量添加店铺。"
---
## 功能特性
`AddTaskModal` 是一个通过 `ref` 调用的弹窗组件，用于添加任务。它支持单个或批量添加店铺，并提供了成功和关闭时的回调。

## 调用方式
```tsx
import AddTaskModal, { AddTaskModalHandle } from '@nibfe/crm-pc-react-components/dist/components/add-task-modal';
```

## 属性 (Props)
| 属性名          | 类型           | 是否必选 | 默认值 | 描述                     |
| --------------- | -------------- | -------- | ------ | ------------------------ |
| `role`          | `ROLE`         | 否       | -      | 角色 (`ROLE.BD` or `ROLE.BDM`) |
| `onModalClose`  | `() => void`   | 否       | -      | 弹窗关闭时的回调         |
| `onSuccess`     | `() => void`   | 否       | -      | 添加任务成功时的回调     |

## 方法 (Handle)
| 方法名  | 类型                                | 描述                                                         |
| ------- | ----------------------------------- | ------------------------------------------------------------ |
| `open`  | `(params: OpenParams) => void`      | 打开弹窗。`params` 包含 `shopData` (单个或多个店铺信息) 和 `canBatchUploadShop` (是否允许批量上传)。 |

## 代码示例
```tsx
import React, { useRef } from 'react';
import AddTaskModal, { AddTaskModalHandle, ROLE } from '@nibfe/crm-pc-react-components/dist/components/add-task-modal';

const MyComponent = () => {
  const modalRef = useRef<AddTaskModalHandle>(null);

  const handleOpenModal = () => {
    modalRef.current?.open({
      shopData: { shopId: '123', shopName: '示例店铺' },
      canBatchUploadShop: true,
    });
  };

  return (
    <>
      <button onClick={handleOpenModal}>添加任务</button>
      <AddTaskModal
        ref={modalRef}
        role={ROLE.BD}
        onSuccess={() => {
          console.log('任务添加成功');
        }}
        onModalClose={() => {
          console.log('弹窗已关闭');
        }}
      />
    </>
  );
};

export default MyComponent;
``` 