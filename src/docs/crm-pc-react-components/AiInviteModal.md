---
title: "AiInviteModal"
description: "一个用于 AI 邀约的全局弹窗组件。"
---
## 功能特性
`AiInviteModal` 是一个通过静态方法 `open` 和 `close` 控制的全局弹窗组件，用于发起 AI 邀约。

## 调用方式
```tsx
import AiInviteModal from '@nibfe/crm-pc-react-components/dist/components/ai-invite-modal';
```

## 方法
| 方法名  | 类型                                | 描述                                                         |
| ------- | ----------------------------------- | ------------------------------------------------------------ |
| `open`  | `(props: ModalProps) => void`      | 打开弹窗。`props` 对象用于配置弹窗。 |
| `close` | `() => void`                        | 关闭弹窗。                                                   |

### open(props: ModalProps)
`ModalProps` 对象的属性如下：

| 属性名                 | 类型          | 是否必选 | 描述                       |
| ---------------------- | ------------- | -------- | -------------------------- |
| `title`                | `string`      | 否       | 弹窗标题。                 |
| `shopData`             | `any`         | 否       | 店铺数据。                 |
| `showImportPrivateSea` | `boolean`     | 否       | 是否显示导入私海按钮。     |
| `zIndex`               | `number`      | 否       | 设置弹窗的 `z-index`。     |
| `destroyOnClose`       | `boolean`     | 否       | 关闭时是否销毁弹窗。       |
| `onSuccess`            | `() => void`  | 否       | 成功回调。                 |

## 代码示例
```tsx
import React from 'react';
import AiInviteModal from '@nibfe/crm-pc-react-components/dist/components/ai-invite-modal';

const MyComponent = () => {
  const handleOpenModal = () => {
    AiInviteModal.open({
      title: '发起 AI 邀约',
      shopData: { id: '123', name: '示例店铺' },
      onSuccess: () => {
        console.log('邀约成功！');
        AiInviteModal.close();
      },
    });
  };

  return (
    <button onClick={handleOpenModal}>AI 邀约</button>
  );
};

export default MyComponent;
``` 