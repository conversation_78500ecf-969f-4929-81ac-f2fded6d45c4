---
title: "@nibfe/crm-pc-react-components 组件库概览"
description: "CRM PC端React组件库的完整使用指南和组件索引"
---

# @nibfe/crm-pc-react-components

一个专为CRM PC端应用设计的React组件库，提供了丰富的业务组件，支持任务管理、沟通记录、计划创建等核心功能。

## 📦 安装

```bash
npm install @nibfe/crm-pc-react-components
# 或
yarn add @nibfe/crm-pc-react-components
# 或  
pnpm add @nibfe/crm-pc-react-components
```

## 🎯 技术栈

- **React**: 17.x
- **TypeScript**: ~4.2.4
- **UI库**: MTD React
- **样式**: SCSS

## 📚 组件列表

### 1. 弹窗组件

#### AddTaskModal - 添加任务弹窗
用于创建新任务的弹窗组件，支持单个或批量添加店铺。

#### AiInviteModal - AI邀约弹窗  
全局AI邀约弹窗，通过静态方法控制显示和隐藏。

#### CreatePlanModal - 创建计划弹窗
用于创建和编辑计划的弹窗组件，支持店铺选择和配置。

### 2. 记录组件

#### CallRecord - 通话记录
展示通话记录列表，通常与音频播放器配合使用。

#### ChatRecord - 聊天记录
展示聊天消息记录，支持多种消息类型（文本、图片、语音、视频等）。

#### AudioPlayer - 音频播放器
可控制的音频播放器组件，支持进度控制和时间回调。

## 🚀 快速开始

```tsx
import React from 'react';
import { 
  AddTaskModal, 
  AiInviteModal, 
  AudioPlayer, 
  CallRecord, 
  ChatRecord, 
  CreatePlanModal 
} from '@nibfe/crm-pc-react-components';

// 引入样式
import '@nibfe/crm-pc-react-components/dist/index.css';

const App = () => {
  return (
    <div>
      {/* 使用组件 */}
    </div>
  );
};

export default App;
```

## 📋 依赖要求

### Peer Dependencies
- React: 17.x
- React DOM: 17.x

### 主要依赖
- @ss/mtd-react: 1.1.7
- @ss/mtd-react3: ^0.9.0
- dayjs: ^1.11.13
- classnames: ^2.3.1

## 🔗 相关链接

- 版本: 0.0.7
- 包名: @nibfe/crm-pc-react-components

## 📖 详细文档

每个组件都有详细的API文档和使用示例，请查看对应的组件文档：

- [AddTaskModal 文档](./add-task-modal.md)
- [AiInviteModal 文档](./ai-invite-modal.md) 
- [AudioPlayer 文档](./audio-player.md)
- [CallRecord 文档](./call-record.md)
- [ChatRecord 文档](./chat-record.md)
- [CreatePlanModal 文档](./create-plan-modal.md)

## 🎨 主题和样式

组件库基于MTD Design系统构建，支持主题定制。默认引入了黄色主题：

```tsx
import '@ss/mtd-react3/lib/style/theme-yellow.css';
import '@ss/mtd-react/lib/style/yellow.css';
```

## 💡 使用建议

1. **弹窗组件**: 推荐使用ref方式调用，便于控制显示时机
2. **记录组件**: 可与音频播放器组合使用，提供更好的用户体验  
3. **样式覆盖**: 如需自定义样式，请在引入组件样式后添加自定义CSS
4. **类型支持**: 所有组件都提供完整的TypeScript类型定义

## 🐛 问题反馈

如遇到使用问题，请联系组件库维护团队。 