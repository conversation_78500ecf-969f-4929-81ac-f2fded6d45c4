3. 接入连接池四步骤（必须）

3.1. 第一步：添加依赖

建议在pom里直接引用inf-bom，inf-bom也是基础架构组件未来在整个公司统一的依赖jar包管理方式，以前业务使用的platform-sdk或dianping-parent依赖管理方式将不再维护

inf-bom ChangeLog   https://km.sankuai.com/page/56534319

第一步：添加POM必须依赖

<dependencyManagement>
   <dependencies>
       <dependency>
           <groupId>com.sankuai</groupId>
           <artifactId>inf-bom</artifactId>
           <version>1.9.10</version>
           <type>pom</type>
           <scope>import</scope>
       </dependency>
    </dependencies>
</dependencyManagement>
<!-- dependencies无需指定版本 -->
<dependencies>
 	 <!--核心依赖：数据源-->
     <dependency>
         <groupId>com.dianping.zebra</groupId>
         <artifactId>zebra-api</artifactId>
     </dependency>
     <dependency>
         <groupId>com.dianping.zebra</groupId>
         <artifactId>zebra-ds-monitor-client</artifactId>
     </dependency>
</dependencies>

3.2. 第二步：添加连接池配置

zebra目前支持的数据源有c3p0，tomcat-jdbc，druid，hikaricp, dbcp和dbcp2。zebra使用的配置是基于c3p0的，zebra会自动把c3p0的连接池配置转化成其他类型的连接池配置，对于不能转的配置，zebra会自动给默认值。多种数据源连接池性能压测结果

基本参数

<bean id="dataSource" class="com.dianping.zebra.group.jdbc.GroupDataSource" init-method="init" destroy-method="close">
    <!-- 必配。指定唯一确定数据库的key-->
    <property name="jdbcRef" value="jdbcref" /> 
    <!-- 选配。指定底层使用的连接池类型，支持"c3p0","tomcat-jdbc","druid","hikaricp","dbcp2"和"dbcp"，推荐使用"druid"或者"dbcp2"，版本2.10.3之后默认值为"druid"，之前版本默认值为"c3p0" -->
    <!-- 不要使用tomcat-jdbc连接池！！！-->
    <property name="poolType" value="druid" /> 
    <!-- 选配。指定连接池的最小连接数，默认值是5。 -->
    <property name="minPoolSize" value="5" />
    <!-- 选配。指定连接池的最大连接数，默认值是20。 -->
    <property name="maxPoolSize" value="20" />
    <!-- 选配。指定连接池的初始化连接数，默认值是5。 -->
    <property name="initialPoolSize" value="5" />
    <!-- 选配。指定连接池的获取连接的超时时间，默认值是1000。 -->
    <property name="checkoutTimeout" value="1000" />
</bean>

全部参数列表

很多时候，zebra提供的默认连接池参数对业务已经足够使用了，但如果不能够覆盖业务的特殊场景，也能够支持业务按照需要定制底层连接池参数，具体请参考  zebra_读写分离_属性列表

在代码中直接使用zebra

如果业务不是在Spring环境中使用zebra的，也可以通过以下代码进行初始化

GroupDataSource dataSource = new GroupDataSource("jdbcRef");
//Set other datasource properties if you want

dataSource.init();

//Now dataSource is ready for use
Connection conn = dataSource.getConnection();

PreparedStatement statment = conn.prepareStatement(sql);
ResultSet resultSet = statment.executeQuery();
while (resultSet.next()) {
  System.out.println(resultSet.getObject(1));
}
 
//Must destory datasource if not use 
dataSource.close();

SpringBoot接入Zebra

如果业务是在Springboot环境中，也可以参考这个文档进行使用  05_zebra_dao_springboot整合

此外，使用XFrame、MDP等框架的zebra组件的同学请看对应框架的文档： XFrame Zebra组件文档 、MDPZebra组件文档

注意事项

1.jdbcurl参数

zebra的默认的jdbcurl包含三个参数：characterEncoding=UTF8&socketTimeout=60000&allowMultiQueries=true（部分创建时间早的db可能会有差异，可在rds平台-zebra配置-jdbcref详情-jdbcurl处检查确认）

如果需要额外的参数比如zeroDateTimeBehavior=convertToNull或者其他参数，可以通过以下方式修改"extraJdbcUrlParams"参数进行设置或者甚至是覆盖；

如果有大事务或者大查询会超过60s的，请也覆盖掉socketTimeout=60000这个参数。

<bean id="dataSource" class="com.dianping.zebra.group.jdbc.GroupDataSource" init-method="init" destroy-method="close">
    <!--添加额外的jdbc url的参数，已param1=value1¶m2=value2的方式注入-->
	<property name="extraJdbcUrlParams" value="param1=value1&amp;param2=value2" /> 
</bean>

配置方法参见: zebra_读写分离_属性列表 中搜索 “extraJdbcUrlParams” 部分内容。

2.Druid的RemoveAbandoned参数

对性能要求高的服务请仔细关注该配置项，参考druid wiki

zebra默认开启此参数防止连接泄漏，如果确认业务上没有操作会缓存连接池中连接，可以人工设置关闭，关闭后连接池性能将提升10%左右。

<bean id="dataSource" class="com.dianping.zebra.group.jdbc.GroupDataSource" init-method="init" destroy-method="close">
  ...
	<property name="setExtendProperties" value-ref="zebraExtendProperties" /> 
  ...
</bean>

<bean id="zebraExtendProperties" class="com.dianping.zebra.single.jdbc.DataSourceExtendProperties">
        <property name="propertyMap">
            <map>
                <entry key="removeAbandoned" value="false" />
            </map>
        </property>
</bean>

性能测试报告参见 RemoveAbandoned性能诊断分析  感谢提供测试支持。

3.utf8mb4支持

10_Zebra_FAQ 如何支持utf8mb4

4.加密问题

zebra版本在2.10.0以上版本支持将用户名密码存入到公司统一的KMS系统中去，并对每一个服务进行授权访问后端DB，在不修改用户代码提供最大易用性的同时保证数据库安全。

3.4. 第四步：添加监控配置

接入了CAT监控后，就可以查看SQL的性能等指标。具体请参考连接池CAT打点介绍了解监控的内容。

CAT需要的配置有：在Java项目的 src/main/resources/META-INF目录下，新建一个文件app.properties , 该文件的内容就只有一行:    app.name=${appkey}，这个appkey对应上海侧的应用名或者北京侧的octo的服务名。
                 
4. 接入zebra-dao（必选）: 参考/Users/<USER>/zebra-dao规范文档.md