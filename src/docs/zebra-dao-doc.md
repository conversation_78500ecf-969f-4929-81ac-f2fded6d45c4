# 1. 作用
zebra-dao是对mybatis-spring的轻量级封装，mybatis-spring主要用于mybatis和spring进行整合。相关基础知识可参考：

mybatis中文官方文档：http://www.mybatis.org/mybatis-3/zh/index.html

mybatis-spring中文官方文档：http://www.mybatis.org/spring/zh/

zebra-dao具备原生mybatis或者mybatis-spring的所有功能，其用法本质上就是mybatis的用法。

# 2. 相关依赖和配置

## 2.1 pom依赖
通过inf-bom进行依赖版本管理:  inf-bom ChangeLog

<!--通过inf-bom进行依赖版本管理-->
<dependencyManagement>
   <dependencies>
       <dependency>
           <groupId>com.sankuai</groupId>
           <artifactId>inf-bom</artifactId>
           <version>1.9.10</version>
           <type>pom</type>
           <scope>import</scope>
       </dependency>
   </dependencies>
</dependencyManagement>

<dependencies>
  <!--zebra-dao依赖，其内部依赖了mybatis、mybatis-spring-->
   <dependency>
   <groupId>com.dianping.zebra</groupId>
   <artifactId>zebra-dao</artifactId>
  </dependency>
  <!--Zebra数据源相关依赖-->
  <dependency>
   <groupId>com.dianping.zebra</groupId>
   <artifactId>zebra-api</artifactId>
  </dependency>
  <!--zebra监控-->
  <dependency>
   <groupId>com.dianping.zebra</groupId>
   <artifactId>zebra-ds-monitor-client</artifactId>
  </dependency>
  <!--Spring 相关依赖，请使用3.0以上版本-->
  <dependency>
   <groupId>org.springframework</groupId>
   <artifactId>spring-context</artifactId>
   <version>3.2.9</version>
  </dependency>
  <dependency>
   <groupId>org.springframework</groupId>
   <artifactId>spring-jdbc</artifactId>
   <version>3.2.9</version>
  </dependency>
</dependencies>

3.2 spring整合

zebra-dao与spring整合主要分为3个步骤：

1、配置数据源，请参考：读写分离接入指南如何配置一个GroupDataSource，或者分库分表接入指南如何配置一个ShardDataSource

2、配置SqlSessionFactoryBean

3、配置ZebraMapperScannerConfigurer。


注意：

1.对于第3步要特别注意, 需要使用zebra-dao提供的ZebraMapperScannerConfigurer来替代mybatis-spring原生的MapperScannerConfigurer。

2.不建议业务使用Mybatis的二级缓存。

3.2.1 方式一：xml方式整合

<!--第1步：配置数据源，省略-->

<!--第2步：配置SqlSessionFactoryBean-->
<bean id="zebraSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
	<!--dataource-->
	<property name="dataSource" ref="datasource"/> 
	<!--Mapper files-->
	<property name="mapperLocations" value="classpath*:config/sqlmap/**/*.xml" />
	<!--这里改成实际entity目录,如果有多个，可以用,;\t\n进行分割-->
	<property name="typeAliasesPackage" value="com.xmd.xxx.entity" /> 
</bean>

<!--第3步：配置ZebraMapperScannerConfigurer-->
<bean class="com.dianping.zebra.dao.mybatis.ZebraMapperScannerConfigurer">
	<!--这里改成实际dao目录,如果有多个，可以用,;\t\n进行分割-->
    <property name="basePackage" value="com.xmd.xxx.dao" />
    <property name="sqlSessionFactoryBeanName" value="zebraSqlSessionFactory"/>
</bean> 

3.2.2 方式二：注解方式整合
@Configuration
public class ZebraConfiguration {
  //第1步：配置数据源
  @Bean
  public GroupDataSource groupDataSource() {
    GroupDataSource groupDataSource = new GroupDataSource();
    groupDataSource.setJdbcRef("zebra");
    groupDataSource.setInitialPoolSize(5);
    groupDataSource.setMaxPoolSize(10);
    groupDataSource.setLazyInit(false);
    groupDataSource.setPoolType("druid");
    //		groupDataSource.setIsJdbcrefGroup(true);
    groupDataSource.init();
    return groupDataSource;
  }
  
  //第2步：配置SqlSessionFactoryBean
	@Bean(name="zebraSqlSessionFactory")
	public SqlSessionFactoryBean sqlSessionFactory(DataSource dataSource) throws IOException {
		SqlSessionFactoryBean ssfb = new SqlSessionFactoryBean();
		ssfb.setDataSource(dataSource);
		PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
		ssfb.setMapperLocations(resolver.getResources("classpath*:config/sqlmap/**/*.xml"));
    ssfb.setTypeAliasesPackage("com.xmd.xxx.entity");
		return ssfb;
	}

  //第3步：配置ZebraMapperScannerConfigurer
	@Bean
	public ZebraMapperScannerConfigurer mapperScannerConfigurer() throws IOException {
		ZebraMapperScannerConfigurer configurer = new ZebraMapperScannerConfigurer();
		configurer.setSqlSessionFactoryBeanName("zebraSqlSessionFactory");
		configurer.setBasePackage("com.xmd.xxx.dao");
		return configurer;
	}
}


下面是一个案例：
本案例介绍：springboot 1.5.9、zebra-api 2.10.6、zebra-dao 0.3.1如何进行整合。

使用XFrame的同学请优先查看 XFrame-Zebra组件文档​。

使用MDP的同学请优先查看 MDP-Zebra组件文档。

注意：业界ORM框架mybatis提供了mybatis-spring-boot-starter，以便于mybatis与springboot的整合。由于zebra-dao对mybatis进行了封装，这个starter已经无法起作用，因此这里并不使用。

项目目录结构如下所示：






