API文档编写指南：

0. API文档字段编写

注： 编写文档需要在仓库根目录创建 SERVICE.DESCRIPTION.xml 文件， 并且包含 < serviceDescs > 段落。

API的所需要生成和显示的字段的名称， 含义等信息在上述列表展示。 下面的部分重点介绍如何在编写这些字段。 目前规范约定字段两种编写方式：

1. 通过注解（ 注释） 的方式在代码中编写这些字段信息

2. 在 SERVICE.DESCRIPTION 文件中的 < services > 段落编写字段信息。

这两种编辑方式是等价的， 体现“ Code is Doc” 的理念， 我们推荐第一种编写方式。 事实上之所以存在第二种方式， 更多地考虑以下几种场景：

1. 部分字段无法与代码结合在一块， 需要在代码之外的地方提供这些字段内容

2. 部分语言暂时不支持

注意： 如果两个字段在不同地方都定义， 那么以Annotation（ Comment + Annotation） 为主。 关于Service.Description更多信息参见后继章节

1. Annotation的注解（ 注释） 方式

1. 不同定义方式之间异同点：

＊ octo.thrift

用thrift作为IDL定义接口， 服务端与客户端不限定实现语言。 thrift本身并不是Java语言， 因而无法直接使用annotation的方式进行文档注解。 thrift本身支持注释， 我们约定thrift的‘ 特殊的注解方式’， 将正常的annotation语句放在注释部分（ 类似于javadoc）。 下面是具体的示例：

namespace java com.meituan.waimai.thrift

/**
* @TypeDoc(
*    description = "地址接口返回结果",
*    fields = {
*       @FieldDoc(name = "code", description = "返回代码。0 成功，其他失败。详见：https:// 123.sankuai.com/km/page/17602801"),
*       @FieldDoc(name = "errorMsg", description = "错误信息")
*    }
* )
*/
struct AddressRes {
 1: i32 code,
 2: string errorMsg,
}

/**
* @TypeDoc(description = "删除用户信息参数,包括用户ID，以及手机号")
*/
struct AddressDelParam {
 /**
  * @FieldDoc(description = "用户id")
  */
 1: i32 clientId,
 /**
  * @FieldDoc(description = "用户手机号")
  */
 2: i32 phone,
}
/**
* @InterfaceDoc(
*     type = "octo.thrift",
*     displayName = "用户地址信息服务",
*	   description = "用户地址信息服务，提供删除和添加用户信息",
*     scenarios = "用于用户信息登记，查询，删除等场合"
* )
*/
service AddressService {

 /** 
  * @MethodDoc(
  *      displayName = "删除用户的地址",
  *    	description = "用于删除用户的地址信息"
  * )
  * @ParamDoc(name = "addressDelParam", description = "地址参数")
  */
 AddressRes deleteAddress(1: AddressDelParam addressDelParam)｝

 ＊ 仅支持Java语言的服务类型

 octo.thrift.annotation, Pigeon, octo.thrift.annotation + Pigeon等这些方式， 都是面向java语言的， 我们直接使用annotation类进行文档注解。 下面是具体的示例：

 ＊ 用Spring框架实现Restful服务类型

 标注方式与上面类似， 不同地方在于参数除了通过URL传递之外， 对于POST方式， 部分数据可以作为Body上传。 返回由两个部分： http返回码及body中数据。 http的返回码有标准含义， 因而不需要特别描述， 通常我们只用annotation只用来描述返回的body部分。

 package com.meituan.waimai.service;

 import com.facebook.swift.service.ThriftMethod;
 import com.facebook.swift.service.ThriftService;
 import com.meituan.waimai.thrift.*;

 import com.meituan.servicecatalog.api.annotations.*;

 @InterfaceDoc(
   type = "octo.thrift.annotation",
   displayName = "用户地址服务",
   description = "用户地址信息服务",
   scenarios = "基于地图POI的用户地址存储与查询，可用于配送类和邮寄类业务"
 )
 @ThriftService
 public interface AddressService {
   @MethodDoc(
     displayName = "批量删除地址",
     description = "用于批量删除用户的地址"
   )
   @ThriftMethod
   public AddressRes batchDeleteAddresses(
     @ParamDoc(description = "批量删除地址入参") AddressBatchParam addressBatchParam);

   @MethodDoc(
     name = "批量获取地址",
     description = "用于批量获取地址",
     parameters = {
       @ParamDoc(
         name = "addressBatchParam",
         description = "批量地址参数，包含用户ID，手机号")
     },
     exceptions = {
       @ExceptionDoc(description = "输入参数错误时抛出该异常")
     }
   )
   @ThriftMethod(exception = {
     @ThriftException(type = com.meituan.service.TestException.class, id = 1)
   })
   public AddressRes batchGetAddress(AddressBatchParam addressBatchParam) throws TestException;
 }

 2. 用于生成文档的Annotation接口定义

 类型

 注解对象

 备注

 说明

 定义

 ServiceDoc

 java： class

 thrift： servicce

 不支持重复注解。

 服务描述。 描述一个服务的用途， 提供了哪些接口。

 git 链接

 InterfaceDoc

 java： class

 thrift： service

 支持重复注解。

 对于用spring实现restful， 类名通常未Controller

 接口描述。 描述接口所提供的功能。

 git 链接

 MethodDoc

 java： method

 thrift： method

 不支持重复注解。

 方法描述。 描述一个方法实现的功能， 需要提供哪些参数， 返回什么值。

 git 链接

 ParamDoc

 java： parameter

 支持重复注解。

 thrift的排版不适合直接对Param进行注解， 需要在Method中间接进行注解

 参数描述。 描述参数基本信息， 默认值和使用方式等。

 git 链接

 TypeDoc

 java： class

 thrift： struct

 不支持重复注解。

 如果参数是基本类型不需要通过Type定义

 参数类型描述。

 git 链接

 FieldDoc

 java： field

 thrift： field

 支持重复注解。

 thrift的排版不适合直接对 Field 进行注解， 需要在 TypeDoc 中间接进行注解

 复杂类型（ Class， Struct） 的成员变量。

 git 链接

 3. 标注规范

 3.1 标注基本原则

 ＊ 就近原则且不影响代码阅读： 对于 @ServiceDoc， @InterfaceDoc及 @TypeDoc直接标注对应的Java类， 对于 @MethodDoc直接标注到方法， 对于 @ParamDoc而言， 如果比较短小， 不影响代码阅读的情况下， 可以直接标注对应的参数

 ＊ 简化原则， 只写必须填写的字段： 很多字段与代码有一定关系， 可以通过代码自动获得。 比如：

 用 @MethodDoc标注一个方法， 则方法名， 返回类型名， 异常名都不需要填写。

 用 @ParamDoc标注参数， 则参数名和参数类型名不需要填写

 有一些某些Annotation类型中存在关联字段， 如果我们可以Annotation标注对象关系可以直接推导出来， 也不需要填写。 比如： @Interface 和 @Method是对同一个对象进行标注（ @InterfaceDoc标注类， @MethodDoc标注该类中的方法）， 则我们不需要设置 @MethodDoc 中 interfaceId字段

 我们用 @MethodDoc和 @ParamDoc标注在同一个方法的时候， 如果此时Method只有一个参数， 那么 @ParamDoc中不需要填写类型名及参数名。 当然对应于多个的话， 那我们就必须要要写参数名， 此时类型名可以缺省（ 因为可以从代码中自动分析出来）。

 ＊ 描述尽量详细准确： 我们生成的文档主要给我们用户阅读， 他们无法像我们对自己服务一样有清晰的了解。 我们需要提供尽量详细的信息便于他们阅读

 3.2 ServiceDoc的标注

 @ServiceDoc只能作用于java中class 或者 thrift中的service。 如果某个服务下面存在多个接口（ InterfaceDoc） 且代码中不存在一个类代表服务（ ServiceDoc）， 我们可以在SERVICE.DESCRIPTION.xml中填写serviceDescs对应部分的字段。 上文表中所有字段， 在SERVICE.DESCRIPTION.xml都有对应的存在。

 第一种情况： @ServiceDoc在java文件

 @ServiceDoc(
     name = "美团外卖地址服务",
     description = "...",
     scenarios = "...",
     notice = "..."）
     @InterfaceDoc(
       type = "octo.thrift.annotation",
       displayName = "用户地址服务",
       description = "用户地址信息服务",
       scenarios = "基于地图POI的用户地址存储与查询，可用于配送类和邮寄类业务"
     ) @ThriftService public interface AddressService {
       ...
     }



     第二种情况： 在thrift的idl文件中

     /**
      * @ServiceDoc(
      *    name = ".....",
      *    description = "......",
      *    scenarios = "......"
      * )
      * 
      * @InterfaceDoc(
      *    type = "octo.thrift",
      *    displayName = "用户地址信息服务",
      *	  description = "用户地址信息服务，提供删除和添加用户信息",
      *    scenarios = "用于用户信息登记，查询，删除等场合"
      * )
      */
     service AddressService {
       ...｝

       3.3 @InterfaceDoc的标注

       对于绝大部分场合下， 我们都是单独使用 @InterfaceDoc对java的中服务类进行注解。 @MethodDoc与 @InterfaceDoc的关系与 @InterfaceDoc与 @ServiceDoc类似， 我们可以在 @interfaceDoc中定义 @MethodDoc， 也可以将 @MethodDoc单独使用。 记住 @MethodDoc不能直接注解类， 直接注解到类中具体方法。 下面两种方式都是正确的：

       方式1（ 不推荐使用）:

         @InterfaceDoc(
           type = "octo.thrift.annotation",
           displayName = "用户地址服务",
           description = "用户地址信息服务",
           scenarios = "基于地图POI的用户地址存储与查询，可用于配送类和邮寄类业务"，
           methods = {  // 如果接口中超过1个方法的，则这里必须要添加name，否则判断Method对象是对那个方法进行注解
             @MethodDoc(name = "AddressService", ...)
           }
         )
       @ThriftService
       public interface AddressService {
         public AddressRes batchDeleteAddresses(AddressBatchParam addressBatchParam);
         public returnType otherMethod(...);
         ...
       }

       方式2（ 推荐使用）:

         @InterfaceDoc(
           type = "octo.thrift.annotation",
           displayName = "用户地址服务",
           description = "用户地址信息服务",
           scenarios = "基于地图POI的用户地址存储与查询，可用于配送类和邮寄类业务"
         )
       @ThriftService
       public interface AddressService {
         @MethodDoc(
           displayName = "批量删除地址",
           description = "用于批量删除用户的地址"
         )
         @ThriftMethod
         public AddressRes batchDeleteAddresses(AddressBatchParam addressBatchParam);
         ...
       }

       下面的方法是错误的,
       因为 @Method不能作用于class／ service

       @InterfaceDoc(
         type = "octo.thrift.annotation",
         displayName = "用户地址服务",
         description = "用户地址信息服务",
         scenarios = "基于地图POI的用户地址存储与查询，可用于配送类和邮寄类业务"
       )
       @MethodDoc(
         displayName = "批量删除地址",
         description = "用于批量删除用户的地址",
         ...
       )
       @ThriftService
       public interface AddressService {
         @ThriftMethod
         public AddressRes batchDeleteAddresses(AddressBatchParam addressBatchParam);
         ...
       }

       3.4 ServiceDoc 与 InterfaceDoc 的数据关系

       当Service中只包含一个Interface时， @ServiceDoc中的描述， 使用场景和注意事项和 @InterfaceDoc没有什么区别， 因而只需要在一个地方填写即可。 如果我们在两个地方都填写的话， 那么这个字段在Service和Interface的文档页中将呈现不同的结果。

       当Service中包含不止一个Interface时， 如果某个 @InterfaceDoc没有填写的部分， 我们会用 @ServiceDoc对应的字段自动填充。 如果几个 @interfaceDoc的某些字段都是一样的话， 那我们建议在 @ServiceDoc中填写。

       综上： 如果 @ServiceDoc和 @InterfaceDoc中某个字段的内容是一样的， 那么我们推荐在 @ServiceDoc中填写而不是 @InterfaceDoc中。

       3.5 多个InterfaceDoc共享一个Java class / interface

       通常我们想为一组接口实现提供多种访问方式， 在这里我们需要多个 @InterfaceDoc annotation共同标注一个Java Class / interface 的情况。 这里我们分为两种情况：

       1. 多个 @InterfaceDoc annotation的字段和内容全部都是一样的， 这里主要场景是pigeon和octo.thrift.annotation.对于这种情况， 我们只需要用一个 @InterfaceDoc注解即可。 此时 @InterfaceDoc的类型选择复合类型， 比如： pigeon＋ octo.thrift.annotation。

       @InterfaceDoc(
         type = "pigeon + octo.thrift.annotation"， // 相当于同时用pigeon和octo.thrift.annotation两种类型interface注解
         displayName = "用户地址服务",
         description = "用户地址信息服务",
         scenarios = "基于地图POI的用户地址存储与查询，可用于配送类和邮寄类业务"
       )
       @ThriftService
       public interface AddressService {
         @MethodDoc(
           displayName = "批量删除地址",
           description = "用于批量删除用户的地址"
         )
         @ThriftMethod
         public AddressRes batchDeleteAddresses(AddressBatchParam addressBatchParam);
         ...
       }

       2. 多个Interface的内容定义在不一样的地方， 比如： restful和pigeon或者restful和octo.thrift.annotation。 对于这种情况下， 我们就需要保留多个。 值得注意的是， 如果多个 @InterfaceDoc注解一个Java class / interface， 我们又用 @MethodDoc直接注解方法的方式时， 我们必须要指定 @MethodDoc属于哪一个 @InterfaceDoc注释。 此时， 我们必须利用 @MethodDoc中Interface ID字段。 例如：

       @InterfaceDoc(
         type = "pigeon + octo.thrift.annotation"， // 相当于同时用pigeon和octo.thrift.annotation两种类型interface注解
         id = 1,
         displayName = "用户地址服务",
         description = "用户地址信息服务",
         scenarios = "基于地图POI的用户地址存储与查询，可用于配送类和邮寄类业务"
       )
       @InterfaceDoc(
         type = "octo.thrift.annotation"， // 注解第三个Interface
         id = 2,
         displayName = "用户地址服务",
         description = "用户地址信息服务",
         scenarios = "基于地图POI的用户地址存储与查询，可用于配送类和邮寄类业务"
       )
       @ThriftService
       public interface AddressService {
         @MethodDoc(
           interfaceId = 1, // 关联第一个interface
           displayName = "批量删除地址",
           description = "用于批量删除用户的地址"
         )

         @ThriftMethod
         public AddressRes batchDeleteAddresses(AddressBatchParam addressBatchParam);
         ...
       }

       需要注意的一件事情是： 目前必须Java8以上才能支持Annotation重复定义语法， 所以按照第二种方式定义， 必须确保使用Java版本不低于8.

       3.6 @MethodDoc的标注

       @MethodDoc主要标注Interface中的方法。 对于Java语言， @MethodDoc主要标注的Java class或者interface中定义的成员方法。 按照3 .3 的说明， @MethodDoc可以单独标注成员方法， 也可以作为 @InterfaceDoc的成员之一， 直接在 @InterfaceDoc注解类对象中赋值。 两种使用方式大同小异， 但是本着“ 就近原则”， 我们推荐独立使用 @MethodDoc注解成员方法的方式。

       目前， 我们支持三种协议： restful， thrift 和 java， 这些方式对于方法的定义有一定差别。 比如restful定义的方法， 需要提供URL， url参数及Post 数据类型， 返回数据类型。 而对于thrift， 需要给出参数的id。 对于Java而言， 并没有什么需要额外增加的。 对于用octo.thrift.annotation定义的接口类型， 我们在方法的定义中更加接近与标准thrift。 对于大家普遍采用缺省id的做法， 我们会采用thrift.annotation的方式自动设置默认值。

       Java类型的 @MethodDoc标注

       @MethodDoc中绝大部分字段都不需要手工充填， 文档生成器会根据 @MethodDoc注解的方法自动提取相关的字段信息。 比如： 方法名(.name),
       返回类型（.returnType）,
       异常（.exception）,
       定义（.definition)。 即使在Method上直接注解参数的话， 我们也只需要给参数名即可。 参数类型等可以自动提取出来。 如果 @MethodDoc与 @InterfaceDoc是作用在同一个类， 且只有一个 @InterfaceDoc标注的实体， 那么我们是不需要填写interfaceId的字段。

     一个好的Java类型定义的接口我们只需要重点填写以下几个部分内容： 文档中显示名（.displayName）, 方法说明(.description), 返回值说明（.returnValueDescription） 及示例代码(.example)。

     @MethodDoc(
       displayName = "删除用户的地址",
       description = "用于删除数据库中用户的地址信息",
       returnValueDescription = "AddressRes包含两个字段，code和errorMsg。" +
       "code为0，表示成功，其他表示失败，errorMsg表示错误信息"
     ) @ThriftMethod public AddressRes deleteAddresses(
       @ParamDoc(description = "删除用户地址信息时，需要提供用户信息，包括用户ID，以及手机号") // @ParamDoc单独注解，不需要提供名字
       AddressDeleParam addressBatchParam); @MethodDoc(
       displayName = "批量获取地址",
       description = "通过用户ID获取用户信息",
       parameters = {
         ParamDoc(name = "addressParam", description = "获取用户地址信息时，需要提供用户信息，包括用户ID，以及手机号") // @ParamDoc 在method里面注解需要指定名称
       },
       returnValueDescription = "AddressVores包含改用户的地址信息，失败时为null",
     ) @ThriftMethod(exception = {
       @ThriftException(type = TestException.class, id = 1)
     }) public AddressVoRes getAddressById(AddressByIdParam addressParam);

     Thrift类型的 @MethodDoc标注

     Thrift类型的 @MethodDoc与Java类型没有本质的区别， 如果有差别的仅仅是Thrift中参数列表中每一个参数都又自己都ID。 这个其实在参数（ @ParamDoc） 这个环节体现出来。 另外， 就是Thrift采用块注释 + annotation的方式。 故而， @ParamDoc无法定义在参数部分， 只能注解到方法。 即便如此， 与上面的 @ServiceDoc与 @InterfaceDoc， @InterfaceDoc与 @MethodDoc关系一样， 我们需要既可以在 @MethodDoc里面直接注解 @ParamDoc（ 对 @MethodDoc.parameters字段直接赋值）， 也可以用 @ParamDoc注解对应的方法。 对于这两种情况下， @ParamDoc.name的字段名必须手工设置， 按照约定参数名与代码中保持一致。

     /**
      * @MethodDoc(
      *     displayName = "删除用户的地址",
      *     description = "用于删除数据库中用户的地址信息",
      *     returnValueDescription = "AddressRes包含两个字段，code和errorMsg。"
      *                               + "code为0，表示成功，其他表示失败，errorMsg表示错误信息"
      * )
      * @ParamDoc(
      *     name = "addressDelParam",
      *     description = "删除用户地址信息时，需要提供用户信息，包括用户ID，以及手机号"
      * ) 
      */
     AddressRes deleteAddress(1: AddressDelParam addressDelParam) throws(1: TException te)

     /**
      * @MethodDoc(
      *     displayName = "获取用户信息",
      *     description = "通过用户ID获取用户信息",
      *     returnValueDescription = "AddressVores包含改用户的地址信息，失败时为null",                 
      *     parameters = {
      *         @ParamDoc(
      *             name = "addressParam",
      *             description = "获取用户地址信息时，需要提供用户信息，包括用户ID，以及手机号"
      *         )
      *     }
      * )
      */
     AddressVoRes getAddressById(AddressByIdParam addressParam) throws(1: TException te)

     注意： octo.thrift.annotation类型， 我们建议更贴近Java类型的方式。 文档生成器会自动解析Thrift相关的annotaiton， 并自动设置对应的字段。



     Restful类型的 @MethodDoc标注：

     Restful类型的标准需要注意以下几点：

     1. 从代码中提取的方法没有任何意义， 必须要提供通过url字段提供URL。

     2. Restful是基于HTTP协议的， 调用方式需要区分GET， POST（ 相当于新增）， PUT（ 类似于POST， 相当于修改， 要求满足幂等性） 和DELETE 方式。 我们必须通过requestType字段说明调用方式

     3. 参数分为两种类型， 一个通过URL的参数列表传递的部分， 另外一个对于用POST or PUT方式， 数据内容放在HTTP的Body中。 对于前者， 我们通过parameters字段定义参数列表， 后者我们放在postData字段中定义。

     4. 在url中定义的参数默认采用 https: // method_url?k1=v1&k2=v2等方式。 如果我们url规则不满足的话，可以在paraDelimiter字段中定义。

     5. 强烈建议给一个示例， 方便大家调用。

     除了上述Restful需要注意的点之外， 其他环节与上面Java类型完全一致。

     @MethodDoc(
       urls = "http:// sankuai.git.com:8080/waimai/userinfo",
       requestMethods = {
         HttpMethod.DET
       },
       description = "获取用户信息,查询参数用户ID，city表示要查询的城市，默认表示从全国查询。" +
       "成功时返回用户所有信息，失败时需要返回原因",
       restExampleUrl = "http:// sankuai.git.com:8080/waimai/userinfo??id=1897878xxxxx&city=beijing",
       restExampleResponseData = "{id:1020 gender:男 age:23}"
     ) public String getInfo(@ParamDoc(description = "用户Id", requiredness = Requiredness.REQUIRED) long userID,
       @ParamDoc(description = "查询城市范围", requiredness = Requiredness.OPTIONAL, defaultValue = "china") String city); @MethodDoc(
       urls = "http:// sankuai.git.com:8080/waimai/userinfo",
       requestMethods = {
         HttpMethod.POST,
         HttpMethod.PATCH
       },
       description = "向数据库注册用户信息，请求时body内容为用户信息，以JSON或XML格式存储。失败时，需要返回失败原因",
       restExampleUrl = "http:// sankuai.git.com:8080/waimai/userinfo",
       restExamplePostData = "userInfo :\n{ id:17809xxxxxx\n gender:male\nage:23\n}", // post 请求body的内容
       restExampleResponseData = "用户信息缺少年龄", // post 方法返回body的内容
     ) public boolean postInfo(@ParamDoc(description = "用户注册信息", requiredness = Requiredness.REQUIRED) UserInfo userInfo);

     3.7 ParamDoc的标注

     @ParamDoc用来标注参数， @ParamDoc可以和 @MethodDoc一样直接标注Java的成员方法， 也可以标注参数。 主要的内容分为三个部分： 参数名， 参数类型， 描述。 一些特殊的场合下， 还需要一些特殊字段， 比如： 对于thrift定义的方法， 参数还有自己的id， 对于restful定义的参数还有是否可以省略， 默认值等。

     @ParamDoc中存在typeName和type两个字段， 前者表示类型的名称， 后者表示类型的定义。 对于基本类型， 我们只需要提供typeName即可， type不需要赋值， 对于复杂类型的话， 我们需要使用type提供复杂类型定义信息。 对于容器类型的话， 我们需要分为两种情况： 容器针对基本类型与基本类型处理方式一样， 我们只需要提供typeName即可。 容器针对复杂类型， 那我们除了提供typeName之外， 还需要提供type用于对复杂类型进行定义。 如果容器针对还是一个容器类型。 那我们只需要关注内部容器即可， 这样递归下去， 最终会是满足上述条件之一。

     记住最核心一点： 只要类型名中含有复杂类型那么我们就需要提供类型的定义。 对于Map这样多类型的容器， 如果K， V都是复杂类型的话， 我们需要对K和V都要进行定义。

     注意： 对于Java定义的方法中参数类型可能是一个非常复杂的类， 比如是一个模版类。 对于复杂参数需要使用 @TypeDoc注解， 如果该复杂类的字段仍包含复杂类， 仍需 @TypeDoc注解， 直到所有均为基本类型。

     类型名： typeName

     类型定义： type

     说明

     int， string， float， double， date...

     无

     基本类型

     MyStruct， MyClass， OtherComplexStruct

     需要 @TypeDoc 对相应对象进行注解

     复杂类型

     list < int > , set < string > , map < int, float >

     无

     容器 + 基本类型

     list < list < int >> list < set < string >>

     无

     嵌套容器 + 基本类型

     list < MyStruct >

     需要用 @TypeDoc 对MyStruct进行注解

     容器 + 复杂类型

     list < map < string, MyClass >> ,

     list <

     map <

     string, map << MyKey, MyValue >>>

     需要对 @TypeDoc 对MyClass, MyKey, MyValue进行注解

     嵌套容器 + 复杂类型

     class A < Template_B, Template_C >

     需要用户自定通过 @TypeDoc定义具体的B和C

     Java复杂类型

     具体示例：

     // 注解自己定义的复杂class
     public void getInfo(@ParamDoc(
       description = "用户的注册信息",
       requiredness = Requiredness.REQUIRED
     ) UserInfor userInfo) // UserInfor 必须使用@Type注解

     // 注解嵌套容器 + 复杂类型
     @ParamDoc(
       name = "firstArg",
       description = "第一个参数的相关描述",
       requiredness = Requiredness.REQUIRED,
       ...
     ) // UserId 需要使用@TypeDoc注解，这里会通过UserId的类名引用其注解 
     @ParamDoc(
       name = "secondArg",
       description = "第二个参数的相关描述",
       requiredness = Requiredness.OPTIONAL,
       defaultValue = ...
     ) // 其中UserId,MyKey,和MyValue 均需要TypeDoc注解，这里会通过类名引用到这些注解
     public boolean putInfo(List < Map < String, UserId >> firstArg, List < Map < string, Map < MyKey, MyValue >>> secondArg);

     3.8 TypeDoc的标注

     @TypeDoc主要用来定义接口中方法对应的参数， 异常及返回值类型。 我们并不关注方法内部实现所涉及到类型。 关于 @TypeDoc与参数类型之间关系， 由3 .7 的表格中定义出来。 我们假设某个方法需要对某个类型进行标注， 那我们推荐的写法是用 @TypeDoc直接标注对应的Java的Class或者Thrift的Struct。 Java的Class内部除了属性（ 成员变量 / 域） 之外， 还有很多方法。 但是这些方法与API调用无关， 所以我们不会提取这部分内容， 所以也不需要标注这部分内容。

     通常来说一个复杂类型的成员变量列表非常类似于参数列表。 每一个字段都有自己的类型及名称。 如果字段类型是基础类型， 我们直接提取即可。 如果字段的类型是复杂类型， 我们需要递归 @TypeDoc再进行标注。 每一个字段的注解我们采用 @FieldDoc注解类型。 @FieldDoc注解其本质于 @ParamDoc并没有任何区别， 因而之前关于 @ParamDoc的约定在这里依然可用（ 比如typeName于type之间的关系）， 需要区别仅仅有一点， @FieldDoc注解作用只能作用于成员变量而不能作用于参数。 @ParamDoc则反之。

     与前面的各种注解类型类似， 文档生成器会自动分析代码， 自动提取每一个字段信息生成 @FieldDoc注解信息填充到 @TypeDoc的.fields 的字段中。 @FieldDoc注解可以在 @TypeDoc中直接赋值， 也可以独立注解成员变量。

     下面是Type的标注示例：

     // @typeDoc 单独注解只存在两种情况，注解thrift IDL中的struct，注解JAVA中的class,基本类型无需关注
     /**
      * @TypeDoc(
      *     description = "地址服务返回结果",
      *     fields = {
      *         @FieldDoc(name = "code", description = "状态码。0 成功，其他详见：https:// 123.sankuai.com/km/page/17602801"),
      *         @FieldDoc(name = "errorMsg", description = "错误信息") // fileds 的parameter和标注的参数顺序一致时，可以省略name,否则必须添加。
      *     }
      * )
      */
     struct AddressRes {
       1: i32 code,
       2: string errorMsg,
     }
     @TypeDoc(
       description = "用户的注册信息，其中id,male,age必须包含",
       fields = {
         @FieldDoc(name = "id", description = "用户id", rule = "手机号码"),
         @FieldDoc(name = "male", description = "用户性别", rule = "必须为男或者女"),
         @FieldDoc(name = "age", description = "用户年龄", rule = "值介于1～120")
       }
     ) public class UserInfo {
       private String id;
       private String male;
       private int age;
       ...
     }

     3.9 ParamDoc， TypeDoc， FieldDoc关系

     @ParamDoc主要用来注解参数， 参数一个重要组织部分就是类型， 所以我们需要 @TypeDoc对一些复杂类型进行描述。 而复杂类型通常由很多字段构成， 每一个字段的描述类似于ParamDoc， 为了区分我们定义 @FieldDoc类型用于描述字段。 字段的类型也可能是一个复杂类型， 进而还需要 @TypeDoc进行描述， 以此递归。

     递归类型的注解方式： @TypeDoc的注解会逐层展开， 形成树状关系。 我们发现如果底层某个类型节点曾经在树中先行定义过， 则我们将不会再进一步展开（ 避免死循环）。

     3.10 ExtensionDoc的标注

     如果有需要显示的内容， 且之前约定定义的字段无法满足的话， 我们可以通过 @ExtensionDoc进行扩展。 @ExtensionDoc字段比较简单：.name 扩展内容的标题或者名称，.format 扩展内容的格式， 格式信息便于前端页面采用不同显示方式。.content， 就是用户提供的内容， 记住这个内容是按照上面的format类型， 格式化好的。 API文档生成器不负责格式转换。

     目前支持的格式有：

     格式类型

     格式说明

     TEXT

     普通的文本类型

     MK

     Markdown类型

     RST

     reStructuredText类型

     HTML

     WEB显示类型

     4. ServiceDescription

     ServiceDescription的引入主要为了解决两个问题： 第一个问题： 提供API文档生成器所需要的索引信息， 比如某个Service是由Thrift定义的， 其文件存在路径在那里。 第二个问题： 上述Annotation注解的方式不适合使用的地方， 我们通过ServiceDescription文件作为补充。

     约定： 所有满足规范的服务对应的仓库必须存在ServiceDescription文件， 且文件存在代码仓库根路径下。 ServiceDescription的文件名为SERVICE.DESCRIPTION.xml， 默认格式为xml。

     ServiceDescription的根Tag为ServiceCatalog， 下面由两组构成： 1. ServiceDesc， 2. ServiceDoc。 其中ServiceDoc的子节点组织方式与上述列表完全一致。

     ServiceDescription的结构说明： SERVICE.DESCRIPTION 详述

     SERVICE.DESCRIPTION.xml 的示例：（ 支持使用xsd来验证xml填写是否正确）

     <
     ? xml version = "1.0"
     encoding = "UTF-8" ? >
     <
     serviceCatalog xmlns = "http:// service.sankuai.com/1.0.0"
     xmlns : xsi = "http:// www.w3.org/2001/XMLSchema-instance"
     xsi : schemaLocation = "http:// service.sankuai.com/1.0.0
     http: // pixel.sankuai.com/repository/releases/com/meituan/apidoc/servicecatalog/1.0.0/servicecatalog-1.0.0.xsd">
     <
     !--Service description-- >
     <
     serviceDescs >
     <
     serviceDesc >
     <
     appkey > com.meituan.test.service.common < /appkey> <
     interfaceDescs >
     <
     interfaceDesc >
     <
     type > pigeon < /type> <
     id > acount < /id> <
     className > com.meituan.test.service.Account < /className> <
     /interfaceDesc> <
     interfaceDesc >
     <
     type > octo.thrift < /type> <
     id > nameservice < /id> <
     idlFile > src / main / resource / thrift / nameservice.thrift < /idlFile> <
     /interfaceDesc> <
     /interfaceDescs> <
     /serviceDesc> <
     serviceDesc >
     <
     appkey > com.meituan.test.pay < /appkey> <
     interfaceDescs >
     <
     interfaceDesc >
     <
     type > restful < /type> <
     id > payment < /id> <
     className > com.meituan.test.pay.Payment < /className> <
     /interfaceDesc> <
     /interfaceDescs> <
     /serviceDesc> <
     /serviceDescs>

     <
     !--API docs-- >
     <
     services >
     <
     service >
     <
     name > Account service < /name> <
     appkey > com.meituan.test.service.common < /appkey> <
     description > Common service example < /description> <
     interfaces >
     <
     interface >
     <
     name > AccountController < /name> <
     displayName > 常规账号操作 < /displayName> <
     id > nameservice < /id> <
     description > Support general account operation < /description> <
     scenarios > Account search, add or update < /scenarios> <
     methods >
     <
     method >
     <
     name > addUser < /name> <
     displayName > 添加用户 < /displayName> <
     description > Add a user < /description> <
     returnType > boolean < /returnType> <
     parameters >
     <
     parameter >
     <
     name > user < /name> <
     description > The user to add < /description> <
     typeName > com.meituan.test.service.account.User < /typeName> <
     types >
     <
     type >
     <
     name > com.meituan.test.service.account.User < /name> <
     description > User type that represents a user < /description> <
     fields >
     <
     field >
     <
     name > name < /name> <
     description > User name < /description> <
     typeName > java.lang.String < /typeName> <
     /field> <
     field >
     <
     name > age < /name> <
     description > User age < /description> <
     typeName > java.lang.Short < /typeName> <
     /field> <
     field >
     <
     name > address < /name> <
     description > Where the user live < /description> <
     typeName > java.lang.String < /typeName> <
     /field> <
     /fields> <
     /type> <
     /types> <
     /parameter> <
     /parameters> <
     /method> <
     method >
     <
     name > deleteUser < /name> <
     displayName > 删除用户 < /displayName> <
     description > Delete a user < /description> <
     returnType > boolean < /returnType> <
     parameters >
     <
     parameter >
     <
     name > user < /name> <
     description > The user to delete < /description> <
     typeName > java.lang.String < /typeName> <
     /parameter> <
     /parameters> <
     /method> <
     /methods> <
     /interface> <
     interface >
     ......
     <
     /interface> <
     /interfaces> <
     /service> <
     service >
     <
     appkey > com.meituan.test.pay < /appkey>
     ......
     <
     /service> <
     /services> <
     /serviceCatalog>