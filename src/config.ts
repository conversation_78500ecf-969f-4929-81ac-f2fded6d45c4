import { config } from "dotenv";
import yargs from "yargs";
import { hideBin } from "yargs/helpers";
import * as path from "path";

// Load environment variables from .env file
config();

export interface ServerConfig {
  port: number;
  sources: string[];
  projectRoot: string;
  configSources: {
    port: "cli" | "env" | "default";
    sources: "cli" | "env" | "default";
    projectRoot: "cli" | "env" | "default";
  };
}

interface CliArgs {
  port?: number;
  source?: string[];
  projectRoot?: string;
}

export function getServerConfig(isStdioMode: boolean): ServerConfig {
  // Parse command line arguments
  const argv = yargs(hideBin(process.argv))
    .options({
      port: {
        type: "number",
        description: "Port to run the server on",
      },
      source: {
        type: "array",
        description: "Document sources to query from (e.g., antd, element-ui)",
        default: [],
      },
      projectRoot: {
        type: "string",
        description: "Project root directory for document scanning",
        alias: ["project-root", "root"],
      },
    })
    .help()
    .version("1.0.0")
    .parseSync() as CliArgs;

  const config: ServerConfig = {
    port: 3333,
    sources: [],
    projectRoot: process.cwd(), // 默认使用当前工作目录
    configSources: {
      port: "default",
      sources: "default",
      projectRoot: "default",
    },
  };

  // Handle PORT
  if (argv.port) {
    config.port = argv.port;
    config.configSources.port = "cli";
  } else if (process.env.PORT) {
    config.port = parseInt(process.env.PORT, 10);
    config.configSources.port = "env";
  }

  // Handle SOURCES
  if (argv.source && argv.source.length > 0) {
    config.sources = argv.source as string[];
    config.configSources.sources = "cli";
  } else if (process.env.DOC_SOURCES) {
    config.sources = process.env.DOC_SOURCES.split(',').map(s => s.trim());
    config.configSources.sources = "env";
  }

  // Handle PROJECT_ROOT
  if (argv.projectRoot) {
    config.projectRoot = path.resolve(argv.projectRoot);
    config.configSources.projectRoot = "cli";
  } else if (process.env.PROJECT_ROOT) {
    config.projectRoot = path.resolve(process.env.PROJECT_ROOT);
    config.configSources.projectRoot = "env";
  }

  // Log configuration sources
  if (!isStdioMode) {
    console.log("\nConfiguration:");
    console.log(`- PORT: ${config.port} (source: ${config.configSources.port})`);
    console.log(`- SOURCES: [${config.sources.join(', ')}] (source: ${config.configSources.sources})`);
    console.log(`- PROJECT_ROOT: ${config.projectRoot} (source: ${config.configSources.projectRoot})`);
    console.log(); // Empty line for better readability
  }

  return config;
}