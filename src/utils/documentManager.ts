/**
 * 文档管理器
 * 负责扫描、解析、索引项目中的Markdown文档
 * 参考 mcp-docs-server 的 DocumentManager，但简化实现
 */
import * as fs from 'fs';
import * as path from 'path';
import { LocalDocConfig, LocalDocItem } from '../types.js';

export interface DocumentMetadata {
  title: string;
  description: string;
  tags?: string[];
}

export interface DocumentIndex {
  path: string;
  relativePath: string;
  category: string;
  docName: string;
  title: string;
  description: string;
  content: string;
  metadata: DocumentMetadata;
  lastModified: Date;
  size: number;
}

export interface DocumentStructure {
  [category: string]: {
    docs: DocumentIndex[];
    subcategories?: DocumentStructure;
  };
}

/**
 * 项目文档管理器
 */
export class ProjectDocumentManager {
  private projectRoot: string;
  private documents: Map<string, DocumentIndex> = new Map();
  private documentStructure: DocumentStructure = {};

  constructor(projectRoot: string) {
    this.projectRoot = projectRoot;
  }

  /**
   * 初始化并扫描所有文档
   */
  async initialize(): Promise<void> {
    console.log(`📚 开始扫描项目文档: ${this.projectRoot}`);
    await this.scanAllDocuments();
    this.buildDocumentStructure();
    console.log(`✅ 文档扫描完成，共找到 ${this.documents.size} 个文档`);
  }

  /**
   * 扫描项目中的所有 Markdown 文档
   */
  private async scanAllDocuments(): Promise<void> {
    await this.scanDirectory(this.projectRoot, '');
  }

  /**
   * 递归扫描目录
   */
  private async scanDirectory(dirPath: string, relativePath: string): Promise<void> {
    try {
      const entries = fs.readdirSync(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        const entryRelativePath = relativePath ? path.join(relativePath, entry.name) : entry.name;

        if (entry.isDirectory()) {
          // 跳过常见的忽略目录
          if (this.shouldSkipDirectory(entry.name)) {
            continue;
          }
          await this.scanDirectory(fullPath, entryRelativePath);
        } else if (entry.isFile() && entry.name.endsWith('.md')) {
          await this.processDocument(fullPath, entryRelativePath);
        }
      }
    } catch (error) {
      console.warn(`跳过目录扫描: ${dirPath} - ${error}`);
    }
  }

  /**
   * 判断是否应该跳过目录
   */
  private shouldSkipDirectory(dirName: string): boolean {
    const skipDirs = [
      'node_modules',
      '.git',
      '.next',
      'dist',
      'build',
      '.nuxt',
      'coverage',
      '.nyc_output',
      'tmp',
      'temp',
      '.cache'
    ];
    return skipDirs.includes(dirName) || dirName.startsWith('.');
  }

  /**
   * 处理单个文档文件
   */
  private async processDocument(filePath: string, relativePath: string): Promise<void> {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const stats = fs.statSync(filePath);
      
      // 提取文档元数据
      const metadata = this.extractDocumentMetadata(content);
      
      // 确定文档分类
      const category = this.determineCategory(relativePath);
      const docName = path.basename(filePath, '.md');

      // 构建文档索引
      const docIndex: DocumentIndex = {
        path: filePath,
        relativePath: relativePath.replace(/\\/g, '/'), // 统一使用正斜杠
        category,
        docName,
        title: metadata.title || docName,
        description: metadata.description || '暂无描述',
        content,
        metadata,
        lastModified: stats.mtime,
        size: stats.size,
      };

      this.documents.set(relativePath, docIndex);
    } catch (error) {
      console.warn(`处理文档失败: ${filePath} - ${error}`);
    }
  }

  /**
   * 从文档内容中提取元数据
   */
  private extractDocumentMetadata(content: string): DocumentMetadata {
    const lines = content.split('\n');
    let title = '';
    let description = '';
    const tags: string[] = [];

    // 检查是否有 frontmatter
    if (content.startsWith('---')) {
      const frontmatterEnd = content.indexOf('---', 3);
      if (frontmatterEnd !== -1) {
        const frontmatter = content.substring(3, frontmatterEnd);
        const yamlLines = frontmatter.split('\n');
        
        for (const line of yamlLines) {
          const trimmed = line.trim();
          if (trimmed.startsWith('title:')) {
            title = trimmed.substring(6).trim().replace(/['"]/g, '');
          } else if (trimmed.startsWith('description:')) {
            description = trimmed.substring(12).trim().replace(/['"]/g, '');
          } else if (trimmed.startsWith('tags:')) {
            // 简单的 tags 解析
            const tagsStr = trimmed.substring(5).trim();
            if (tagsStr.startsWith('[') && tagsStr.endsWith(']')) {
              const tagsArray = tagsStr.slice(1, -1).split(',');
              tags.push(...tagsArray.map(t => t.trim().replace(/['"]/g, '')));
            }
          }
        }
      }
    }

    // 如果没有找到 frontmatter 中的标题，查找第一个 H1 标题
    if (!title) {
      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed.startsWith('# ')) {
          title = trimmed.substring(2).trim();
          break;
        }
      }
    }

    // 如果没有找到描述，查找第一个非标题段落
    if (!description) {
      let foundTitle = false;
      for (const line of lines) {
        const trimmed = line.trim();
        
        if (foundTitle && trimmed && !trimmed.startsWith('#') && !trimmed.startsWith('```')) {
          description = trimmed;
          break;
        }
        
        if (trimmed.startsWith('# ')) {
          foundTitle = true;
        }
      }
    }

    return {
      title: title || path.basename('未命名文档'),
      description: description || '暂无描述',
      tags: tags.length > 0 ? tags : undefined,
    };
  }

  /**
   * 根据文件路径确定文档分类
   */
  private determineCategory(relativePath: string): string {
    const parts = relativePath.split('/');
    
    if (parts.length === 1) {
      return 'root';
    }

    // 构建层级分类
    const categories = [];
    for (let i = 0; i < parts.length - 1; i++) {
      categories.push(parts[i]);
    }

    return categories.join('/');
  }

  /**
   * 构建文档结构树
   */
  private buildDocumentStructure(): void {
    this.documentStructure = {};

    for (const doc of this.documents.values()) {
      const categoryParts = doc.category === 'root' ? [] : doc.category.split('/');
      let currentLevel = this.documentStructure;

      // 构建嵌套结构
      for (const part of categoryParts) {
        if (!currentLevel[part]) {
          currentLevel[part] = {
            docs: [],
            subcategories: {},
          };
        }
        currentLevel = currentLevel[part].subcategories!;
      }

      // 添加文档到对应分类
      const finalCategory = categoryParts.length > 0 ? categoryParts[categoryParts.length - 1] : 'root';
      if (!currentLevel[finalCategory]) {
        currentLevel[finalCategory] = {
          docs: [],
        };
      }
      
      if (!currentLevel[finalCategory].docs) {
        currentLevel[finalCategory].docs = [];
      }
      
      currentLevel[finalCategory].docs.push(doc);
    }
  }

  /**
   * 获取所有文档
   */
  getAllDocuments(): DocumentIndex[] {
    return Array.from(this.documents.values());
  }

  /**
   * 根据相对路径获取文档
   */
  getDocument(relativePath: string): DocumentIndex | undefined {
    return this.documents.get(relativePath);
  }

  /**
   * 获取文档结构
   */
  getDocumentStructure(): DocumentStructure {
    return this.documentStructure;
  }

  /**
   * 搜索文档
   */
  searchDocuments(query: string, options: {
    category?: string;
    limit?: number;
    includeContent?: boolean;
  } = {}): DocumentIndex[] {
    const { category, limit = 10, includeContent = false } = options;
    const lowerQuery = query.toLowerCase();
    const results: DocumentIndex[] = [];

    for (const doc of this.documents.values()) {
      // 分类过滤
      if (category && !doc.category.includes(category)) {
        continue;
      }

      // 内容匹配
      const matchesTitle = doc.title.toLowerCase().includes(lowerQuery);
      const matchesDescription = doc.description.toLowerCase().includes(lowerQuery);
      const matchesContent = includeContent && doc.content.toLowerCase().includes(lowerQuery);
      const matchesCategory = doc.category.toLowerCase().includes(lowerQuery);
      const matchesFileName = doc.docName.toLowerCase().includes(lowerQuery);

      if (matchesTitle || matchesDescription || matchesContent || matchesCategory || matchesFileName) {
        results.push(doc);
      }

      if (results.length >= limit) {
        break;
      }
    }

    return results;
  }

  /**
   * 获取文档统计信息
   */
  getStats(): {
    totalDocuments: number;
    totalCategories: number;
    categoriesStats: { [category: string]: number };
    totalSize: number;
  } {
    const categoriesStats: { [category: string]: number } = {};
    let totalSize = 0;

    for (const doc of this.documents.values()) {
      categoriesStats[doc.category] = (categoriesStats[doc.category] || 0) + 1;
      totalSize += doc.size;
    }

    return {
      totalDocuments: this.documents.size,
      totalCategories: Object.keys(categoriesStats).length,
      categoriesStats,
      totalSize,
    };
  }

  /**
   * 生成工具描述
   */
  generateToolDescription(): string {
    const stats = this.getStats();
    const structure = this.getDocumentStructure();

    let description = `📁 本地项目文档智能搜索工具 | 项目内部资源检索专家

🎯 **核心功能**: 智能搜索项目中的所有 Markdown 文档
📊 **扫描统计**: 共 ${stats.totalDocuments} 个文档，${stats.totalCategories} 个分类，总大小 ${(stats.totalSize / 1024).toFixed(2)} KB

🚨 **优先触发场景** (AI应自动调用):
• "当前仓库有哪些存量组件"
• "项目有什么组件/工具类"
• "查看项目文档/API文档"
• "项目规范/开发指南"
• "使用AI友好文档工具"

📦 **NPM包查询优先级**（仅针对包名格式查询）:
当用户明确提到npm包格式时（如@scope/package-name），此工具为最后选择：
1. search_external_docs 优先 - 查找官方文档
2. search_npm_docs 次之 - 查找项目依赖
3. **本工具最后** - 查找项目内部文档（可能包含相关说明）

💡 **智能判断说明**:
- 对于组件名查询（如"Button"、"表格组件"）→ AI根据上下文自主判断最合适的工具
- 对于工具类查询（如"utils"、"helper"）→ AI根据上下文自主判断最合适的工具
- 对于项目资源查询（如"存量组件"）→ 直接使用本工具
- 对于包名查询（如"@nibfe/dz-form-core"）→ 按上述优先级作为最后选择

🔍 **专门检测**:
• 🧩 **存量组件**: 自动识别React/Vue等组件
• 🛠️ **工具类**: 扫描utility、helper函数
• 🪝 **Hook检测**: 识别自定义Hook
• 🛡️ **服务类**: API服务、业务逻辑
• 📝 **类型定义**: interface、type等类型文件
• 📊 **常量配置**: 配置文件、常量定义

⚠️ **避免误用**: 
- 不要将此工具用于第三方组件库查询
- 不要将此工具用于npm包文档查询
- 主要用于项目内部组件、工具类、文档的查询

📂 **文档结构目录**:
`;

    // 生成文档结构描述
    description += this.generateStructureDescription(structure, 0);

    description += `
💡 **智能查询策略**:
• 🔍 **关键词匹配**: 自动匹配文档标题、描述、分类
• 📊 **批量检索**: 支持多关键词同时查询
• 🎯 **模糊搜索**: 支持中英文混合、别名匹配
• 🔗 **结构化展示**: 按文件夹层级组织结果

🔍 **使用建议**: 查询项目相关内容时，优先使用此工具进行全面扫描！`;

    return description;
  }

  /**
   * 生成结构描述（递归）
   */
  private generateStructureDescription(structure: DocumentStructure, depth: number): string {
    let result = '';
    const indent = '  '.repeat(depth);

    for (const [category, data] of Object.entries(structure)) {
      if (category === 'root') {
        if (data.docs.length > 0) {
          result += `${indent}📄 **根目录文档** (${data.docs.length}个):\n`;
          for (const doc of data.docs.slice(0, 3)) {
            result += `${indent}  • ${doc.title} - ${doc.description}\n`;
          }
          if (data.docs.length > 3) {
            result += `${indent}  • ... 还有 ${data.docs.length - 3} 个文档\n`;
          }
        }
      } else {
        result += `${indent}📁 **${category}** (${data.docs.length}个文档):\n`;
        for (const doc of data.docs.slice(0, 2)) {
          result += `${indent}  • ${doc.title} - ${doc.description}\n`;
        }
        if (data.docs.length > 2) {
          result += `${indent}  • ... 还有 ${data.docs.length - 2} 个文档\n`;
        }

        // 递归处理子分类
        if (data.subcategories) {
          result += this.generateStructureDescription(data.subcategories, depth + 1);
        }
      }
    }

    return result;
  }
} 