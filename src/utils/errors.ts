export enum ErrorCode {
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  PARSE_ERROR = 'PARSE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  CONFIG_ERROR = 'CONFIG_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  SCAN_ERROR = 'SCAN_ERROR',
  SEARCH_ERROR = 'SEARCH_ERROR'
}

export class MCPError extends Error {
  constructor(
    public code: ErrorCode,
    message: string,
    public cause?: Error,
    public context?: Record<string, any>
  ) {
    super(message);
    this.name = 'MCPError';
    
    // 保持错误堆栈
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, MCPError);
    }
  }

  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      context: this.context,
      cause: this.cause?.message
    };
  }
}

export function handleError(error: unknown, context?: Record<string, any>): MCPError {
  if (error instanceof MCPError) {
    // 如果提供了新的上下文，合并它
    if (context) {
      error.context = { ...error.context, ...context };
    }
    return error;
  }
  
  if (error instanceof Error) {
    // 根据错误类型和消息推断错误码
    const code = inferErrorCode(error);
    return new MCPError(code, error.message, error, context);
  }
  
  return new MCPError(
    ErrorCode.PARSE_ERROR, 
    String(error), 
    undefined, 
    context
  );
}

function inferErrorCode(error: Error): ErrorCode {
  const message = error.message.toLowerCase();
  
  if (message.includes('enoent') || message.includes('file not found')) {
    return ErrorCode.FILE_NOT_FOUND;
  }
  
  if (message.includes('eacces') || message.includes('permission')) {
    return ErrorCode.PERMISSION_ERROR;
  }
  
  if (message.includes('fetch') || message.includes('network')) {
    return ErrorCode.NETWORK_ERROR;
  }
  
  if (message.includes('config') || message.includes('invalid')) {
    return ErrorCode.CONFIG_ERROR;
  }
  
  return ErrorCode.PARSE_ERROR;
}

export function createFileNotFoundError(filePath: string): MCPError {
  return new MCPError(
    ErrorCode.FILE_NOT_FOUND,
    `文件未找到: ${filePath}`,
    undefined,
    { filePath }
  );
}

export function createScanError(directory: string, cause?: Error): MCPError {
  return new MCPError(
    ErrorCode.SCAN_ERROR,
    `扫描目录失败: ${directory}`,
    cause,
    { directory }
  );
}

export function createSearchError(query: string, cause?: Error): MCPError {
  return new MCPError(
    ErrorCode.SEARCH_ERROR,
    `搜索失败: ${query}`,
    cause,
    { query }
  );
} 