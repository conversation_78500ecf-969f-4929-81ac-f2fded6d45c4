/**
 * 项目根目录自动检测工具
 */
import * as fs from "fs";
import * as path from "path";

/**
 * 项目根目录标识文件列表
 * 这些文件通常出现在项目根目录
 */
const PROJECT_ROOT_INDICATORS = [
  'package.json',
  '.git',
  'yarn.lock',
  'pnpm-lock.yaml',
  'package-lock.json',
  'tsconfig.json',
  '.gitignore',
  'docs.json'
];

/**
 * 自动检测项目根目录
 * @param startPath 开始搜索的路径，默认为当前工作目录
 * @param maxDepth 最大向上搜索层级，默认为10层
 * @returns 项目根目录路径，如果找不到则返回当前工作目录
 */
export function detectProjectRoot(startPath?: string, maxDepth: number = 10): string {
  let currentPath = startPath || process.cwd();
  let depth = 0;

  // 规范化路径
  currentPath = path.resolve(currentPath);

  while (depth < maxDepth) {
    // 检查当前目录是否包含项目根目录标识文件
    const hasRootIndicator = PROJECT_ROOT_INDICATORS.some(indicator => {
      const fullPath = path.join(currentPath, indicator);
      return fs.existsSync(fullPath);
    });

    if (hasRootIndicator) {
      console.log(`检测到项目根目录: ${currentPath}`);
      return currentPath;
    }

    // 获取父目录
    const parentPath = path.dirname(currentPath);
    
    // 如果已经到达根目录，停止搜索
    if (parentPath === currentPath) {
      break;
    }

    currentPath = parentPath;
    depth++;
  }

  // 如果找不到项目根目录，使用当前工作目录
  const fallbackPath = process.cwd();
  console.warn(`未找到项目根目录标识文件，使用当前工作目录: ${fallbackPath}`);
  return fallbackPath;
}

/**
 * 获取项目根目录
 * 优先级：环境变量 PROJECT_ROOT > 自动检测 > 当前工作目录
 * @returns 项目根目录路径
 */
export function getProjectRoot(): string {
  // 1. 优先使用环境变量指定的路径
  if (process.env.PROJECT_ROOT) {
    const envPath = path.resolve(process.env.PROJECT_ROOT);
    if (fs.existsSync(envPath)) {
      console.log(`使用环境变量指定的项目根目录: ${envPath}`);
      return envPath;
    } else {
      console.warn(`环境变量 PROJECT_ROOT 指定的路径不存在: ${envPath}`);
    }
  }

  // 2. 自动检测项目根目录
  return detectProjectRoot();
}

/**
 * 验证目录是否为有效的项目根目录
 * @param projectPath 要验证的路径
 * @returns 是否为有效的项目根目录
 */
export function isValidProjectRoot(projectPath: string): boolean {
  try {
    if (!fs.existsSync(projectPath)) {
      return false;
    }

    const stat = fs.statSync(projectPath);
    if (!stat.isDirectory()) {
      return false;
    }

    // 检查是否包含至少一个项目根目录标识文件
    return PROJECT_ROOT_INDICATORS.some(indicator => {
      const fullPath = path.join(projectPath, indicator);
      return fs.existsSync(fullPath);
    });
  } catch (error) {
    console.error(`验证项目根目录失败: ${projectPath}`, error);
    return false;
  }
}

/**
 * 获取项目信息
 * @param projectRoot 项目根目录
 * @returns 项目基本信息
 */
export function getProjectInfo(projectRoot: string): {
  name: string;
  version: string;
  hasDocsJson: boolean;
  hasPackageJson: boolean;
  hasGit: boolean;
} {
  const packageJsonPath = path.join(projectRoot, 'package.json');
  const docsJsonPath = path.join(projectRoot, 'docs.json');
  const gitPath = path.join(projectRoot, '.git');

  let projectName = path.basename(projectRoot);
  let projectVersion = 'unknown';

  // 尝试读取 package.json 获取项目信息
  if (fs.existsSync(packageJsonPath)) {
    try {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
      projectName = packageJson.name || projectName;
      projectVersion = packageJson.version || projectVersion;
    } catch (error) {
      console.warn('读取 package.json 失败:', error);
    }
  }

  return {
    name: projectName,
    version: projectVersion,
    hasDocsJson: fs.existsSync(docsJsonPath),
    hasPackageJson: fs.existsSync(packageJsonPath),
    hasGit: fs.existsSync(gitPath)
  };
} 