import { logger } from './logger.js';

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

export interface CacheOptions {
  defaultTTL?: number;
  maxSize?: number;
  enableLogging?: boolean;
}

export class Cache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private options: Required<CacheOptions>;

  constructor(options: CacheOptions = {}) {
    this.options = {
      defaultTTL: options.defaultTTL ?? 300000, // 5分钟
      maxSize: options.maxSize ?? 1000,
      enableLogging: options.enableLogging ?? false
    };
  }

  set(key: string, data: T, ttl?: number): void {
    const ttlMs = ttl ?? this.options.defaultTTL;
    const now = Date.now();

    // 如果缓存已满，清理最少使用的条目
    if (this.cache.size >= this.options.maxSize) {
      this.evictLeastUsed();
    }

    this.cache.set(key, {
      data,
      timestamp: now,
      ttl: ttlMs,
      accessCount: 0,
      lastAccessed: now
    });

    if (this.options.enableLogging) {
      logger.debug(`缓存设置: ${key}, TTL: ${ttlMs}ms`);
    }
  }

  get(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) {
      if (this.options.enableLogging) {
        logger.debug(`缓存未命中: ${key}`);
      }
      return null;
    }

    const now = Date.now();

    // 检查是否过期
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      if (this.options.enableLogging) {
        logger.debug(`缓存过期: ${key}`);
      }
      return null;
    }

    // 更新访问统计
    entry.accessCount++;
    entry.lastAccessed = now;

    if (this.options.enableLogging) {
      logger.debug(`缓存命中: ${key}, 访问次数: ${entry.accessCount}`);
    }

    return entry.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    const result = this.cache.delete(key);
    if (this.options.enableLogging && result) {
      logger.debug(`缓存删除: ${key}`);
    }
    return result;
  }

  clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    if (this.options.enableLogging) {
      logger.info(`缓存清空: 删除了 ${size} 个条目`);
    }
  }

  getStats() {
    const entries = Array.from(this.cache.values());
    const now = Date.now();
    
    return {
      totalEntries: this.cache.size,
      maxSize: this.options.maxSize,
      expired: entries.filter(e => now - e.timestamp > e.ttl).length,
      averageAccessCount: entries.reduce((sum, e) => sum + e.accessCount, 0) / entries.length || 0,
      oldestEntry: Math.min(...entries.map(e => e.timestamp)),
      newestEntry: Math.max(...entries.map(e => e.timestamp))
    };
  }

  private evictLeastUsed(): void {
    if (this.cache.size === 0) return;

    let leastUsedKey: string | null = null;
    let leastAccessed = Infinity;
    let oldestTimestamp = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      // 优先淘汰访问次数少的，其次是最旧的
      if (entry.accessCount < leastAccessed || 
          (entry.accessCount === leastAccessed && entry.lastAccessed < oldestTimestamp)) {
        leastUsedKey = key;
        leastAccessed = entry.accessCount;
        oldestTimestamp = entry.lastAccessed;
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
      if (this.options.enableLogging) {
        logger.debug(`缓存淘汰: ${leastUsedKey} (访问次数: ${leastAccessed})`);
      }
    }
  }

  // 清理过期条目
  cleanup(): number {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (this.options.enableLogging && cleaned > 0) {
      logger.info(`缓存清理: 删除了 ${cleaned} 个过期条目`);
    }

    return cleaned;
  }
} 