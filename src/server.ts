import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { ServerConfig } from "./config";
import { ResourceDescription } from "./types";
import { getProjectInfo } from "./utils/projectDetection.js";
import { ProjectDocumentManager } from "./utils/documentManager.js";
import * as fs from "fs";
import * as path from "path";
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 工具索引文件
 * 导出所有文档查询相关的工具函数
 */
import { registerLocalDocsQueryTool } from "./tools/localDocsQuery.js";
import { registerExtDocsQueryTool } from "./tools/extDocsQuery.js";
import { registerNpmDocsQueryTool } from "./tools/npmDocsQuery.js";
import { registerComponentScannerTool } from "./tools/componentScanner.js";

/**
 * 从远端服务器获取资源描述
 */
async function fetchResourceDescriptions(sources: string[]): Promise<ResourceDescription[]> {
  const resourceDescriptions: ResourceDescription[] = [];
  
  // 如果没有指定sources，返回空数组
  if (!sources || sources.length === 0) {
    return resourceDescriptions;
  }

  for (const source of sources) {
    try {
      // 根据source名称构造API URL
      const apiUrl = process.env.RESOURCE_API_BASE_URL || 'https://api.example.com';
      const url = `${apiUrl}/resources/${source}/description`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(process.env.RESOURCE_API_KEY && {
            'Authorization': `Bearer ${process.env.RESOURCE_API_KEY}`
          })
        },
      });

      if (response.ok) {
        const data = await response.json() as ResourceDescription;
        resourceDescriptions.push({
          source: source,
          components: data.components || [],
          description: data.description || `${source} 组件库`,
          apiUrl: data.apiUrl
        });
      } else {
        console.warn(`Failed to fetch description for source: ${source}`);
        // 提供默认的资源描述
        resourceDescriptions.push({
          source: source,
          components: [],
          description: `${source} 组件库（描述获取失败，请检查网络连接）`,
        });
      }
    } catch (error) {
      console.error(`Error fetching description for source ${source}:`, error);
      // 提供默认的资源描述
      resourceDescriptions.push({
        source: source,
        components: [],
        description: `${source} 组件库（描述获取失败）`,
      });
    }
  }

  return resourceDescriptions;
}

// 导出服务器创建函数，使其可以作为库导入
export async function aiFriendlyDocsMCPServer(config: ServerConfig) {
  // 使用配置中的项目根目录
  const projectRoot = config.projectRoot;
  const projectInfo = getProjectInfo(projectRoot);
  
  console.log(`项目信息: ${projectInfo.name} v${projectInfo.version}`);
  console.log(`项目根目录: ${projectRoot}`);
  console.log(`配置状态: docs.json=${projectInfo.hasDocsJson ? '✓' : '✗'}, package.json=${projectInfo.hasPackageJson ? '✓' : '✗'}, git=${projectInfo.hasGit ? '✓' : '✗'}`);
  
  // 初始化文档管理器并扫描项目文档
  console.log(`📚 初始化文档管理器...`);
  const documentManager = new ProjectDocumentManager(projectRoot);
  await documentManager.initialize();
  
  const stats = documentManager.getStats();
  console.log(`📊 文档扫描统计: ${stats.totalDocuments} 个文档，${stats.totalCategories} 个分类`);

  // 获取资源描述
  const resourceDescriptions = await fetchResourceDescriptions(config.sources);

  const server = new McpServer(
    {
      name: "ai-friendly-knowledge-mcp",
      version: "1.0.0",
    },
    {
      capabilities: {
        logging: {},
        tools: {},
        prompts: {},
      },
    }
  );

  server.prompt("使用AI友好文档查询工具", () => {
    return {
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: `# AI友好文档查询工具 🔍

## 📋 项目概览
- **项目名称**: ${projectInfo.name} v${projectInfo.version}
- **项目根目录**: ${projectRoot}
- **配置状态**: ${projectInfo.hasDocsJson ? '✅ docs.json 已配置' : '⚠️ 使用默认配置'} | ${projectInfo.hasPackageJson ? '✅ package.json 存在' : '❌ 无 package.json'} | ${projectInfo.hasGit ? '✅ Git 仓库' : '📁 非Git项目'}

## 🎯 自动触发场景
当用户查询以下内容时，AI应该**优先调用**相应的MCP工具：

### 📁 本地文档查询触发词 → 使用 \`search_local_docs\`
- "当前仓库有哪些存量组件"
- "项目有什么组件/工具类" 
- "查看项目文档/规范"
- "API文档/接口文档"
- "安装指南/配置文档"
- "开发规范/最佳实践"
- "项目结构/架构文档"

### 🌐 外部文档查询触发词 → 使用 \`search_external_docs\`
- "xx组件怎么用" (如Button、Table等)
- "Ant Design/Element UI/React组件"
- "第三方库文档"
- "UI组件库API"
- "开源项目使用方法"

### 📦 NPM包查询触发词 → 使用 \`search_npm_docs\`
- "项目依赖包文档"
- "xx包怎么用" (如axios、lodash等)
- "npm包API文档"
- "依赖包使用方法"

## 🛠️ 可用工具详情

### 1. 📁 本地文档智能搜索 \`search_local_docs\`
**核心功能**: 查询项目内部文档资源，包括存量组件、工具类、API文档
**优先使用场景**:
- ✅ "当前仓库有哪些存量组件"
- ✅ "项目有什么工具类"
- ✅ "查看项目文档"
- ✅ "API参考文档"

**调用示例**:
\`\`\`
search_local_docs({"docNames": ["组件", "工具", "API", "指南"]})
\`\`\`

### 2. 🌐 外部文档智能搜索 \`search_external_docs\`
**核心功能**: 查询第三方组件库和开源项目文档
${resourceDescriptions.length > 0 ? `**已配置文档源**: ${resourceDescriptions.map(r => r.source).join(', ')}` : '**未配置外部源**: 需要使用 --source 参数配置'}

**调用示例**:
\`\`\`
search_external_docs({"componentNames": ["Button", "Table"], "sources": ["antd"]})
\`\`\`

### 3. 📦 NPM包文档智能搜索 \`search_npm_docs\`
**核心功能**: 查询项目依赖的npm包文档和API

**调用示例**:
\`\`\`
search_npm_docs({"packageNames": ["axios", "lodash"]})
\`\`\`

## 💡 AI调用指导原则

### 🚨 优先级规则
1. **最高优先级**: 用户明确提到"使用AI友好文档工具"
2. **高优先级**: 查询"当前仓库/项目"的组件、工具类、文档
3. **中优先级**: 查询具体组件名称或第三方库
4. **基础优先级**: 查询npm包或依赖相关

### 🎯 智能匹配策略
- 包含"仓库/项目/当前"关键词 → 优先使用 \`search_local_docs\`
- 包含npm包名（@scope/package-name格式）→ **强制优先使用 \`search_external_docs\`**
- 包含具体组件名 → 根据名称智能选择工具
- 不确定时 → 优先尝试 \`search_local_docs\`

### ⚠️ **特别重要**: NPM包查询优先级
当用户查询包含npm包格式（如@nibfe/crm-pc-react-components）时：
1. **🥇 必须优先使用 \`search_external_docs\`** - 查找本地项目文档和官方文档
2. 🥈 如果没找到，再使用 \`search_npm_docs\` - 查找项目依赖信息
3. 🥉 最后使用 \`search_local_docs\` - 查找项目内部文档

### 📖 调用最佳实践
1. **自动推断参数**: 根据用户问题智能提取关键词
2. **批量查询**: 一次调用查询多个相关文档
3. **渐进查询**: 先查本地，再查外部，最后查npm
4. **结果整合**: 将多个工具结果进行整合展示

---
💻 **重要提醒**: 遇到文档查询相关问题时，AI应该**主动调用**相应工具，而不是直接回答！
  `,
          },
        },
      ],
    };
  });
  
  // 注册所有文档查询工具
  await registerAllDocTools(server, projectRoot, documentManager, resourceDescriptions);
  
  return server;
}

async function registerAllDocTools(
  server: McpServer, 
  projectRoot: string, 
  documentManager: ProjectDocumentManager,
  resourceDescriptions: ResourceDescription[]
) {
  // 注册存量组件扫描工具（最高优先级，专门处理"当前仓库有哪些存量组件"类问题）
  registerComponentScannerTool(server, projectRoot);
  
  // 注册本地文档查询工具（传入文档管理器实例）
  registerLocalDocsQueryTool(server, documentManager);
  
  // 注册远端文档查询工具（传入资源描述）
  registerExtDocsQueryTool(server, resourceDescriptions);
  
  // 注册npm包文档查询工具
  registerNpmDocsQueryTool(server, projectRoot);
  
  return server;
} 