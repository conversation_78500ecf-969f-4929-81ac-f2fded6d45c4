/**
 * 远端文档查询工具
 * 支持查询外部文档资源，优先查询本地@/docs文件夹中的npm包文档，然后查询远端服务器
 */
import { z } from 'zod';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { ResourceDescription } from '../types.js';
import { logger } from '../utils/logger.js';
import { handleError, ErrorCode, MCPError } from '../utils/errors.js';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 远端文档查询接口定义
 */
interface ExtDocSearchRequest {
  query: string;
  sources?: string[];
  limit?: number;
}

interface ExtDocContent {
  id: string;
  title: string;
  content: string;
  source: string;
  type: 'component' | 'method' | 'guide';
  description?: string;
}

interface ExtDocSearchResponse {
  success: boolean;
  data: ExtDocContent[];
  total: number;
  message?: string;
}

/**
 * HTTP请求工具函数（使用Node.js内置http模块）
 */
async function makeHttpRequest(
  url: string,
  options: {
    method: 'GET' | 'POST';
    headers?: Record<string, string>;
    body?: string;
  }
): Promise<Response> {
  try {
    // 在Node.js 18+中，fetch已经是全局可用的
    if (typeof globalThis.fetch !== 'undefined') {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

      try {
        const response = await globalThis.fetch(url, {
          method: options.method,
          headers: options.headers,
          body: options.body,
          signal: controller.signal,
        });
        clearTimeout(timeoutId);
        return response;
      } catch (error) {
        clearTimeout(timeoutId);
        throw error;
      }
    } else {
      // 如果fetch不可用，使用Node.js内置的http/https模块
      const { default: https } = await import('https');
      const { default: http } = await import('http');
      const { URL } = await import('url');

      return new Promise((resolve, reject) => {
        const parsedUrl = new URL(url);
        const isHttps = parsedUrl.protocol === 'https:';
        const client = isHttps ? https : http;

        const requestOptions = {
          hostname: parsedUrl.hostname,
          port: parsedUrl.port || (isHttps ? 443 : 80),
          path: parsedUrl.pathname + parsedUrl.search,
          method: options.method,
          headers: options.headers || {},
          timeout: 10000,
        };

        const req = client.request(requestOptions, res => {
          let data = '';
          res.on('data', chunk => {
            data += chunk;
          });

          res.on('end', () => {
            // 创建一个类似fetch Response的对象
            const response = {
              ok: res.statusCode! >= 200 && res.statusCode! < 300,
              status: res.statusCode!,
              statusText: res.statusMessage || '',
              headers: res.headers,
              json: async () => JSON.parse(data),
              text: async () => data,
            } as unknown as Response;

            resolve(response);
          });
        });

        req.on('error', error => {
          reject(
            new MCPError(
              ErrorCode.NETWORK_ERROR,
              `网络请求失败: ${error.message}`,
              error
            )
          );
        });

        req.on('timeout', () => {
          req.destroy();
          reject(new MCPError(ErrorCode.NETWORK_ERROR, '请求超时'));
        });

        if (options.body) {
          req.write(options.body);
        }

        req.end();
      });
    }
  } catch (error) {
    if (error instanceof Error) {
      throw new MCPError(
        ErrorCode.NETWORK_ERROR,
        `网络请求失败: ${error.message}`,
        error
      );
    }
    throw error;
  }
}

/**
 * 远端文档API客户端
 */
class ExtDocsApiClient {
  private baseUrl: string;
  private apiKey?: string;
  private projectRoot: string;

  constructor(
    baseUrl: string = process.env.EXT_DOCS_API_URL || '',
    apiKey?: string,
    projectRoot?: string
  ) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey || process.env.EXT_DOCS_API_KEY;
    this.projectRoot = projectRoot || this.findProjectRoot();
  }

  /**
   * 智能查找项目根目录
   */
  private findProjectRoot(): string {
    // 获取当前模块的目录（ES模块兼容方式）
    const currentModuleUrl = import.meta.url;
    const currentModulePath = currentModuleUrl.startsWith('file://')
      ? currentModuleUrl.slice(7)
      : currentModuleUrl;
    const currentDir = path.dirname(currentModulePath);

    // 尝试多种方式找到项目根目录
    const candidates = [
      process.cwd(),
      process.env.PROJECT_ROOT,
      // 从当前模块路径推断（假设在dist/tools/目录下）
      path.resolve(currentDir, '../../..'),
      path.resolve(currentDir, '../..'),
      // 从package.json位置推断
      this.findPackageJsonRoot(currentDir),
    ].filter(Boolean);

    for (const candidate of candidates) {
      if (!candidate) continue;

      // 检查是否是有效的项目根目录
      if (this.isValidProjectRoot(candidate)) {
        logger.debug(`找到项目根目录: ${candidate}`);
        return candidate;
      }
    }

    // 如果都没找到，返回当前工作目录
    logger.warn('未找到有效的项目根目录，使用当前工作目录');
    return process.cwd();
  }

  /**
   * 检查是否是有效的项目根目录
   */
  private isValidProjectRoot(dir: string): boolean {
    try {
      // 检查是否存在package.json或者docs目录
      const hasPackageJson = fs.existsSync(path.join(dir, 'package.json'));
      const hasDocsDir = fs.existsSync(path.join(dir, 'src', 'docs')) ||
                        fs.existsSync(path.join(dir, 'dist', 'docs'));

      return hasPackageJson || hasDocsDir;
    } catch {
      return false;
    }
  }

  /**
   * 从package.json位置推断项目根目录
   */
  private findPackageJsonRoot(startDir?: string): string | null {
    // 获取起始目录
    let currentDir = startDir;
    if (!currentDir) {
      const currentModuleUrl = import.meta.url;
      const currentModulePath = currentModuleUrl.startsWith('file://')
        ? currentModuleUrl.slice(7)
        : currentModuleUrl;
      currentDir = path.dirname(currentModulePath);
    }

    // 向上查找package.json
    for (let i = 0; i < 10; i++) { // 最多向上查找10级
      const packageJsonPath = path.join(currentDir, 'package.json');
      if (fs.existsSync(packageJsonPath)) {
        return currentDir;
      }

      const parentDir = path.dirname(currentDir);
      if (parentDir === currentDir) break; // 已到根目录
      currentDir = parentDir;
    }

    return null;
  }

  /**
   * 检查是否为npm包名格式
   */
  public isNpmPackageName(query: string): boolean {
    // 检查是否为 @scope/package-name 或 package-name 格式
    return /^(@[a-z0-9-~][a-z0-9-._~]*\/)?[a-z0-9-~][a-z0-9-._~]*$/.test(query);
  }

  /**
   * 将npm包名转换为本地文档路径
   * 例如: @nibfe/crm-pc-react-components -> crm-pc-react-components
   */
  private npmPackageToLocalPath(packageName: string): string {
    if (packageName.startsWith('@')) {
      // @scope/package-name -> package-name
      const parts = packageName.split('/');
      if (parts.length === 2) {
        return parts[1];
      }
    }
    return packageName;
  }

  /**
   * 获取文档基础路径
   * 智能检测项目根目录和文档路径
   */
  private getDocsBasePath(): string {
    // 尝试多种方式确定项目根目录
    const possibleRoots = [
      process.cwd(),
      this.projectRoot,
      // 如果当前是根目录，尝试从模块路径推断
      path.dirname(path.dirname(__dirname)), // 从当前文件位置推断
      // 尝试从环境变量获取
      process.env.PROJECT_ROOT,
    ].filter(Boolean);

    let projectRoot = this.projectRoot;
    let docsBasePath = '';

    // 寻找包含docs目录的项目根目录
    for (const root of possibleRoots) {
      if (!root) continue;

      const distDocsPath = path.join(root, 'dist', 'docs');
      const srcDocsPath = path.join(root, 'src', 'docs');

      logger.debug(`检查路径: ${root}`);
      logger.debug(`  - dist/docs: ${distDocsPath} (存在: ${fs.existsSync(distDocsPath)})`);
      logger.debug(`  - src/docs: ${srcDocsPath} (存在: ${fs.existsSync(srcDocsPath)})`);

      if (fs.existsSync(distDocsPath)) {
        projectRoot = root;
        docsBasePath = distDocsPath;
        logger.debug(`✅ 找到dist/docs路径: ${docsBasePath}`);
        break;
      } else if (fs.existsSync(srcDocsPath)) {
        projectRoot = root;
        docsBasePath = srcDocsPath;
        logger.debug(`✅ 找到src/docs路径: ${docsBasePath}`);
        break;
      }
    }

    // 如果都没找到，使用默认逻辑
    if (!docsBasePath) {
      const isDistExecution =
        process.cwd().includes('node_modules') ||
        fs.existsSync(path.join(projectRoot, 'dist', 'docs'));

      docsBasePath = isDistExecution
        ? path.join(projectRoot, 'dist', 'docs')
        : path.join(projectRoot, 'src', 'docs');

      logger.debug(`⚠️ 使用默认逻辑: isDistExecution=${isDistExecution}, docsBasePath=${docsBasePath}`);
    }

    logger.debug(`📍 最终文档基础路径: ${docsBasePath}`);

    return docsBasePath;
  }

  /**
   * 在指定npm包中搜索特定组件的文档
   */
  public async searchLocalNpmDocsForComponent(
    packageName: string,
    componentName: string
  ): Promise<ExtDocSearchResponse> {
    try {
      const localPath = this.npmPackageToLocalPath(packageName);
      const docsBasePath = this.getDocsBasePath();
      const docsPath = path.join(docsBasePath, localPath);

      logger.debug(
        `在本地npm包中搜索组件: ${packageName} -> ${componentName} -> ${docsPath}`
      );

      if (!fs.existsSync(docsPath)) {
        logger.debug(`本地文档路径不存在: ${docsPath}`);
        return {
          success: false,
          data: [],
          total: 0,
          message: `本地文档路径不存在: ${localPath}    ${docsPath}`,
        };
      }

      const results: ExtDocContent[] = [];
      await this.scanLocalDocsDirectoryForComponent(
        docsPath,
        packageName,
        componentName,
        results
      );

      if (results.length > 0) {
        logger.debug(
          `找到组件 ${componentName} 的本地文档: ${results.length} 个文件`
        );
        return {
          success: true,
          data: results,
          total: results.length,
          message: `从本地文档找到 ${componentName} 组件的 ${results.length} 个相关文档`,
        };
      } else {
        return {
          success: false,
          data: [],
          total: 0,
          message: `在 ${packageName} 中未找到 ${componentName} 组件的文档`,
        };
      }
    } catch (error) {
      logger.error(
        `搜索本地npm包组件文档失败: ${packageName}/${componentName}`,
        error
      );
      return {
        success: false,
        data: [],
        total: 0,
        message: `搜索本地文档时发生错误: ${error instanceof Error ? error.message : '未知错误'}`,
      };
    }
  }

  /**
   * 搜索本地npm包文档
   */
  public async searchLocalNpmDocs(
    packageName: string
  ): Promise<ExtDocSearchResponse> {
    try {
      const localPath = this.npmPackageToLocalPath(packageName);
      const docsBasePath = this.getDocsBasePath();
      const docsPath = path.join(docsBasePath, localPath);

      logger.debug(`尝试查找本地npm包文档: ${packageName} -> ${docsPath}`);

      if (!fs.existsSync(docsPath)) {
        logger.debug(`本地文档路径不存在: ${docsPath}`);
        return {
          success: false,
          data: [],
          total: 0,
          message: `本地文档路径不存在: ${localPath}`,
        };
      }

      const results: ExtDocContent[] = [];
      await this.scanLocalDocsDirectory(docsPath, packageName, results);

      if (results.length > 0) {
        logger.debug(`找到本地npm包文档: ${results.length} 个文件`);
        return {
          success: true,
          data: results,
          total: results.length,
          message: `从本地文档找到 ${results.length} 个相关文档`,
        };
      } else {
        return {
          success: false,
          data: [],
          total: 0,
          message: `本地文档目录为空: ${localPath}`,
        };
      }
    } catch (error) {
      logger.error(`搜索本地npm包文档失败: ${packageName}`, error);
      return {
        success: false,
        data: [],
        total: 0,
        message: `搜索本地文档时发生错误: ${error instanceof Error ? error.message : '未知错误'}`,
      };
    }
  }

  /**
   * 递归扫描本地文档目录，搜索特定组件
   */
  private async scanLocalDocsDirectoryForComponent(
    dirPath: string,
    packageName: string,
    componentName: string,
    results: ExtDocContent[],
    currentPath: string = ''
  ): Promise<void> {
    try {
      const entries = fs.readdirSync(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        const relativePath = currentPath
          ? `${currentPath}/${entry.name}`
          : entry.name;

        if (entry.isDirectory()) {
          // 递归扫描子目录
          await this.scanLocalDocsDirectoryForComponent(
            fullPath,
            packageName,
            componentName,
            results,
            relativePath
          );
        } else if (entry.isFile() && entry.name.endsWith('.md')) {
          // 检查文件名或内容是否匹配组件名
          const fileName = entry.name.replace('.md', '');
          const content = fs.readFileSync(fullPath, 'utf-8');

          // 匹配逻辑：文件名匹配或内容中包含组件名
          const isMatch = this.isComponentMatch(
            fileName,
            content,
            componentName
          );

          if (isMatch) {
            // 提取文档标题和描述
            const { title, description } =
              this.extractDocumentMetadata(content);

            results.push({
              id: `local-${packageName}-${relativePath}`,
              title: title || fileName,
              content: content,
              source: `本地文档 (${packageName})`,
              type: this.determineDocType(entry.name, content),
              description:
                description || `${packageName} ${componentName} 组件文档`,
            });
          }
        }
      }
    } catch (error) {
      logger.warn(`扫描本地文档目录失败: ${dirPath}`, error);
    }
  }

  /**
   * 检查文件是否匹配指定的组件名
   */
  private isComponentMatch(
    fileName: string,
    content: string,
    componentName: string
  ): boolean {
    const lowerFileName = fileName.toLowerCase();
    const lowerComponentName = componentName.toLowerCase();
    const lowerContent = content.toLowerCase();

    logger.debug(
      `检查文件匹配: fileName="${fileName}", componentName="${componentName}"`
    );

    // 1. 文件名完全匹配（忽略大小写）
    if (lowerFileName === lowerComponentName) {
      logger.debug(
        `文件名完全匹配: ${lowerFileName} === ${lowerComponentName}`
      );
      return true;
    }

    // 2. 文件名包含组件名（处理kebab-case和camelCase）
    const kebabComponentName = componentName
      .replace(/([A-Z])/g, '-$1')
      .toLowerCase()
      .replace(/^-/, '');
    logger.debug(`kebab转换: ${componentName} -> ${kebabComponentName}`);

    if (
      lowerFileName.includes(lowerComponentName) ||
      lowerFileName.includes(kebabComponentName)
    ) {
      logger.debug(
        `文件名包含匹配: ${lowerFileName} 包含 ${lowerComponentName} 或 ${kebabComponentName}`
      );
      return true;
    }

    // 3. 内容中包含组件名（在标题或重要位置）
    const lines = content.split('\n');
    for (let i = 0; i < Math.min(10, lines.length); i++) {
      // 只检查前10行
      const line = lines[i].toLowerCase();
      if (
        line.includes(`# ${lowerComponentName}`) ||
        line.includes(`## ${lowerComponentName}`) ||
        line.includes(`\`${componentName}\``) ||
        line.includes(`"${componentName}"`)
      ) {
        logger.debug(`内容匹配: 在第${i + 1}行找到组件名`);
        return true;
      }
    }

    logger.debug(`文件不匹配: ${fileName} 与 ${componentName} 不匹配`);
    return false;
  }

  /**
   * 递归扫描本地文档目录
   */
  private async scanLocalDocsDirectory(
    dirPath: string,
    packageName: string,
    results: ExtDocContent[],
    currentPath: string = ''
  ): Promise<void> {
    try {
      const entries = fs.readdirSync(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        const relativePath = currentPath
          ? `${currentPath}/${entry.name}`
          : entry.name;

        if (entry.isDirectory()) {
          // 递归扫描子目录
          await this.scanLocalDocsDirectory(
            fullPath,
            packageName,
            results,
            relativePath
          );
        } else if (entry.isFile() && entry.name.endsWith('.md')) {
          // 处理Markdown文件
          const content = fs.readFileSync(fullPath, 'utf-8');
          const stats = fs.statSync(fullPath);

          // 提取文档标题和描述
          const { title, description } = this.extractDocumentMetadata(content);

          results.push({
            id: `local-${packageName}-${relativePath}`,
            title: title || entry.name.replace('.md', ''),
            content: content,
            source: `本地文档 (${packageName})`,
            type: this.determineDocType(entry.name, content),
            description: description || `${packageName} 本地文档`,
          });
        }
      }
    } catch (error) {
      logger.warn(`扫描本地文档目录失败: ${dirPath}`, error);
    }
  }

  /**
   * 提取文档元数据
   */
  private extractDocumentMetadata(content: string): {
    title: string;
    description: string;
  } {
    const lines = content.split('\n');
    let title = '';
    let description = '';

    // 检查是否有 frontmatter
    if (content.startsWith('---')) {
      const frontmatterEnd = content.indexOf('---', 3);
      if (frontmatterEnd !== -1) {
        const frontmatter = content.substring(3, frontmatterEnd);
        const yamlLines = frontmatter.split('\n');

        for (const line of yamlLines) {
          const trimmed = line.trim();
          if (trimmed.startsWith('title:')) {
            title = trimmed.substring(6).trim().replace(/['"]/g, '');
          } else if (trimmed.startsWith('description:')) {
            description = trimmed.substring(12).trim().replace(/['"]/g, '');
          }
        }
      }
    }

    // 如果没有从 frontmatter 中找到标题，查找第一个H1标题
    if (!title) {
      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed.startsWith('# ')) {
          title = trimmed.substring(2).trim();
          break;
        }
      }
    }

    // 如果没有从 frontmatter 中找到描述，查找第一个非标题段落作为描述
    if (!description) {
      let foundTitle = false;
      for (const line of lines) {
        const trimmed = line.trim();

        if (
          foundTitle &&
          trimmed &&
          !trimmed.startsWith('#') &&
          !trimmed.startsWith('```')
        ) {
          description =
            trimmed.length > 100 ? trimmed.substring(0, 100) + '...' : trimmed;
          break;
        }

        if (trimmed.startsWith('# ')) {
          foundTitle = true;
        }
      }
    }

    return { title, description };
  }

  /**
   * 根据文件名和内容确定文档类型
   */
  private determineDocType(
    fileName: string,
    content: string
  ): 'component' | 'method' | 'guide' {
    const lowerFileName = fileName.toLowerCase();
    const lowerContent = content.toLowerCase();

    if (
      lowerFileName.includes('component') ||
      lowerContent.includes('组件') ||
      lowerContent.includes('component')
    ) {
      return 'component';
    } else if (
      lowerFileName.includes('api') ||
      lowerFileName.includes('method') ||
      lowerContent.includes('方法') ||
      lowerContent.includes('api')
    ) {
      return 'method';
    } else {
      return 'guide';
    }
  }

  /**
   * 搜索远端文档
   */
  async searchDocs(
    request: ExtDocSearchRequest
  ): Promise<ExtDocSearchResponse> {
    try {
      if (!this.baseUrl) {
        logger.warn('远端文档服务未配置');
        return {
          success: false,
          data: [],
          total: 0,
          message: '远端文档服务未配置，请设置 EXT_DOCS_API_URL 环境变量',
        };
      }

      const url = new URL('/api/docs/search', this.baseUrl);
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (this.apiKey) {
        headers['Authorization'] = `Bearer ${this.apiKey}`;
      }

      logger.debug(`搜索远端文档: ${request.query}`, {
        sources: request.sources,
      });

      const response = await makeHttpRequest(url.toString(), {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new MCPError(
          ErrorCode.NETWORK_ERROR,
          `API请求失败: ${response.status} ${response.statusText}`
        );
      }

      const data = (await response.json()) as ExtDocSearchResponse;
      logger.debug(`远端文档搜索完成: 找到 ${data.data?.length || 0} 个结果`);
      return data;
    } catch (error) {
      const mcpError = handleError(error, {
        operation: 'searchDocs',
        query: request.query,
        sources: request.sources,
      });
      logger.error('远端文档搜索失败', mcpError);

      return {
        success: false,
        data: [],
        total: 0,
        message: `搜索失败: ${mcpError.message}`,
      };
    }
  }

  /**
   * 根据资源ID获取完整文档内容
   */
  async getDocById(docId: string): Promise<ExtDocContent | null> {
    try {
      if (!this.baseUrl) {
        logger.warn('远端文档服务未配置');
        return null;
      }

      const url = new URL(`/api/docs/${docId}`, this.baseUrl);
      const headers: Record<string, string> = {};

      if (this.apiKey) {
        headers['Authorization'] = `Bearer ${this.apiKey}`;
      }

      logger.debug(`获取文档详情: ${docId}`);

      const response = await makeHttpRequest(url.toString(), {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        throw new MCPError(
          ErrorCode.NETWORK_ERROR,
          `API请求失败: ${response.status} ${response.statusText}`
        );
      }

      const data = (await response.json()) as {
        success: boolean;
        data: ExtDocContent;
      };
      return data.success ? data.data : null;
    } catch (error) {
      const mcpError = handleError(error, { operation: 'getDocById', docId });
      logger.error('获取文档详情失败', mcpError);
      return null;
    }
  }
}

/**
 * 工具参数类型定义
 */
interface ExtDocsToolParams {
  componentNames?: string[];
  sources?: string[];
  limit?: number;
}

/**
 * 注册远端文档查询工具
 * @param server MCP服务器实例
 * @param resourceDescriptions 资源描述列表
 */
export function registerExtDocsQueryTool(
  server: McpServer,
  resourceDescriptions: ResourceDescription[] = []
): void {
  const apiClient = new ExtDocsApiClient();

  // 生成工具描述文本
  const toolDescription = generateToolDescription(resourceDescriptions);

  logger.info(
    `注册远端文档查询工具，配置了 ${resourceDescriptions.length} 个文档源`
  );

  server.tool(
    'search_external_docs',
    toolDescription,
    {
      componentNames: z
        .array(z.string())
        .optional()
        .describe(
          '🔍 要查询的组件名称列表。例如: ["Button", "Table", "Form"] 或 ["导航", "按钮"]'
        ),
      sources: z
        .array(z.string())
        .optional()
        .describe('📚 指定查询的文档源。例如: ["antd", "element-ui", "react"]'),
      limit: z
        .number()
        .optional()
        .default(10)
        .describe('📊 返回结果数量限制（默认10，最大50）'),
    },
    async (params: ExtDocsToolParams) => {
      try {
        const { componentNames, sources, limit = 10 } = params;
        let results: ExtDocContent[] = [];
        const executionLog: string[] = [];

        executionLog.push(`🚀 开始远端文档查询: ${new Date().toISOString()}`);
        executionLog.push(`📥 输入参数: componentNames=${JSON.stringify(componentNames)}, sources=${JSON.stringify(sources)}, limit=${limit}`);

        logger.debug('开始远端文档查询', { componentNames, sources, limit });

        if (componentNames && componentNames.length > 0) {
          // 查询指定的外部文档
          executionLog.push(`🔍 查询指定组件: ${componentNames.join(', ')}`);
          logger.debug(`查询指定组件: ${componentNames.join(', ')}`);

          // 检查sources中是否有npm包名
          const npmPackageSources =
            sources?.filter(source => apiClient.isNpmPackageName(source)) || [];
          const otherSources =
            sources?.filter(source => !apiClient.isNpmPackageName(source)) ||
            [];

          executionLog.push(`📦 NPM包源检测: ${npmPackageSources.length} 个npm包源, ${otherSources.length} 个其他源`);
          executionLog.push(`📦 NPM包源列表: ${JSON.stringify(npmPackageSources)}`);
          executionLog.push(`🌐 其他源列表: ${JSON.stringify(otherSources)}`);

          for (const docName of componentNames) {
            executionLog.push(`\n🎯 处理组件: ${docName}`);
            let searchResult: ExtDocSearchResponse;
            let foundInLocal = false;

            // 如果sources中有npm包名，优先在对应的本地目录中搜索特定组件
            if (npmPackageSources.length > 0) {
              executionLog.push(`📦 检测到npm包源，开始本地搜索组件: ${docName}`);
              logger.debug(
                `检测到npm包源: ${npmPackageSources.join(', ')}，在本地搜索组件: ${docName}`
              );

              for (const packageName of npmPackageSources) {
                executionLog.push(`🔍 在包 ${packageName} 中搜索组件 ${docName}`);
                const localResult =
                  await apiClient.searchLocalNpmDocsForComponent(
                    packageName,
                    docName
                  );

                executionLog.push(`📊 搜索结果: success=${localResult.success}, dataLength=${localResult.data.length}, message="${localResult.message}"`);

                if (localResult.success && localResult.data.length > 0) {
                  // 本地找到了文档，使用本地结果
                  results.push(...localResult.data);
                  foundInLocal = true;
                  executionLog.push(`✅ 在本地包 ${packageName} 中找到组件 ${docName}，找到 ${localResult.data.length} 个结果`);
                  logger.debug(
                    `在本地包 ${packageName} 中找到组件 ${docName}，找到 ${localResult.data.length} 个结果`
                  );
                  break; // 找到就停止搜索其他包
                } else {
                  // 记录搜索失败的详细信息，但不直接返回
                  executionLog.push(`❌ 在本地包 ${packageName} 中未找到组件 ${docName}: ${localResult.message}`);
                  logger.debug(
                    `在本地包 ${packageName} 中未找到组件 ${docName}: ${localResult.message}`
                  );
                }
              }
            } else {
              executionLog.push(`⚠️ 没有检测到npm包源，跳过本地搜索`);
            }

            // 如果本地没找到，或者没有npm包源，则查询远端或检查组件名是否为npm包名
            if (!foundInLocal) {
              if (apiClient.isNpmPackageName(docName)) {
                logger.debug(`检测到npm包名格式: ${docName}，优先查询本地文档`);

                // 先查询本地npm包文档
                const localResult = await apiClient.searchLocalNpmDocs(docName);

                if (localResult.success && localResult.data.length > 0) {
                  // 本地找到了文档，使用本地结果
                  searchResult = localResult;
                  logger.debug(
                    `本地npm包文档查询成功: ${docName}，找到 ${localResult.data.length} 个结果`
                  );
                } else {
                  // 本地没找到，查询远端
                  logger.debug(`本地npm包文档未找到: ${docName}，转向远端查询`);
                  searchResult = await apiClient.searchDocs({
                    query: docName,
                    sources: otherSources.length > 0 ? otherSources : sources,
                    limit: Math.ceil(limit / componentNames.length),
                  });
                }
              } else {
                // 不是npm包名格式，直接查询远端
                searchResult = await apiClient.searchDocs({
                  query: docName,
                  sources: otherSources.length > 0 ? otherSources : sources,
                  limit: Math.ceil(limit / componentNames.length),
                });
              }

              if (searchResult && searchResult.success) {
                results.push(...searchResult.data);
                logger.debug(
                  `组件 ${docName} 查询完成，找到 ${searchResult.data.length} 个结果`
                );
              } else if (searchResult) {
                logger.warn(
                  `组件 ${docName} 查询失败: ${searchResult.message}`
                );
              }
            }
          }
        } else {
          // 如果没有指定具体文档，返回概览信息
          logger.debug('执行概览查询');

          const overviewResult = await apiClient.searchDocs({
            query: '组件库概览',
            sources: sources,
            limit: limit,
          });

          if (overviewResult.success) {
            results = overviewResult.data;
            logger.debug(`概览查询完成，找到 ${results.length} 个结果`);
          } else {
            logger.warn(`概览查询失败: ${overviewResult.message}`);
          }
        }

        // 构建详细调试信息
        const cwd = process.cwd();
        const distDocsExists = fs.existsSync(path.join(cwd, 'dist', 'docs'));
        const srcDocsExists = fs.existsSync(path.join(cwd, 'src', 'docs'));

        executionLog.push(`\n🏁 执行完成，总共找到 ${results.length} 个结果`);

        const debugInfo = {
          timestamp: new Date().toISOString(),
          inputParams: { componentNames, sources, limit },
          executionLog: executionLog,
          environment: {
            cwd: cwd,
            nodeVersion: process.version,
            platform: process.platform,
            cwdContainsNodeModules: cwd.includes('node_modules'),
            distDocsExists: distDocsExists,
            srcDocsExists: srcDocsExists,
            distDocsPath: path.join(cwd, 'dist', 'docs'),
            srcDocsPath: path.join(cwd, 'src', 'docs')
          },
          npmPackageDetection: {
            npmPackageSources: sources?.filter(source => apiClient.isNpmPackageName(source)) || [],
            otherSources: sources?.filter(source => !apiClient.isNpmPackageName(source)) || []
          },
          searchResults: {
            totalResults: results.length,
            resultSummary: results.map(r => ({
              id: r.id,
              title: r.title,
              source: r.source,
              type: r.type,
              contentLength: r.content ? r.content.length : 0
            }))
          }
        };

        // 构建响应文本
        const responseText = buildExtDocsResponse(
          results,
          componentNames,
          sources
        );

        // 添加调试信息到响应文本
        const debugText = `
# 🐛 详细调试信息

## 📊 执行统计
- **时间戳**: ${debugInfo.timestamp}
- **输入参数**: ${JSON.stringify(debugInfo.inputParams, null, 2)}
- **结果数量**: ${debugInfo.searchResults.totalResults}

## 🔍 环境检测
- **当前工作目录**: ${debugInfo.environment.cwd}
- **Node版本**: ${debugInfo.environment.nodeVersion}
- **平台**: ${debugInfo.environment.platform}
- **工作目录包含node_modules**: ${debugInfo.environment.cwdContainsNodeModules}
- **dist/docs存在**: ${debugInfo.environment.distDocsExists}
- **src/docs存在**: ${debugInfo.environment.srcDocsExists}
- **dist/docs路径**: ${debugInfo.environment.distDocsPath}
- **src/docs路径**: ${debugInfo.environment.srcDocsPath}

## 📦 包源检测
- **NPM包源**: ${JSON.stringify(debugInfo.npmPackageDetection.npmPackageSources)}
- **其他源**: ${JSON.stringify(debugInfo.npmPackageDetection.otherSources)}

## 📋 执行日志
\`\`\`
${debugInfo.executionLog.join('\n')}
\`\`\`

## 📋 搜索结果摘要
${debugInfo.searchResults.resultSummary.length > 0
  ? debugInfo.searchResults.resultSummary.map(r => `- **${r.title}** (${r.type}) - ${r.source} [内容长度: ${r.contentLength}]`).join('\n')
  : '- 无搜索结果'}

---

${responseText}`;

        logger.info(`远端文档查询完成，返回 ${results.length} 个结果`);

        return {
          content: [
            {
              type: 'text',
              text: debugText,
            },
          ],
        };
      } catch (error) {
        const mcpError = handleError(error, {
          operation: 'search_external_docs',
          params,
        });
        logger.error('远端文档查询失败', mcpError);

        return {
          isError: true,
          content: [
            {
              type: 'text',
              text: `查询远端文档时发生错误: ${mcpError.message}`,
            },
          ],
        };
      }
    }
  );
}

/**
 * 生成工具描述文本
 */
function generateToolDescription(
  resourceDescriptions: ResourceDescription[]
): string {
  const baseDescription = `🌐 外部第三方文档智能搜索工具 | 官方组件库文档查询专家

🎯 **核心功能**:
• 🔍 智能搜索第三方组件库官方文档
• 📋 批量查询多个组件的API和用法示例
• 🔗 获取官方文档链接和权威代码示例
• 🎨 提供最佳实践和官方设计模式
• ⚡ 优先查询本地项目文档，再查询远端服务器
• 📦 **专门处理npm包格式查询（@scope/package-name）**

🚨 **最高优先级使用场景**:
• 🎯 **NPM包组件查询**：@nibfe/crm-pc-react-components、@company/ui-lib 等
• 🎯 **指定包中的组件**：查询特定npm包中的组件文档
• 🎯 **本地组件库文档**：优先从项目本地docs目录查找
• 查询知名第三方组件库（React、Vue、Antd、Element等）
• 寻找官方API文档和使用指南

📦 **NPM包查询绝对优先级**:
⚠️ **重要**：当用户查询包含npm包格式（@scope/package-name）时，**必须优先使用此工具**！
1. **🥇 此工具最高优先级** - 查找本地项目文档和官方文档
2. 🥈 search_npm_docs 次之 - 查找项目依赖信息
3. 🥉 search_local_docs 最后 - 查找项目内部文档

🎯 **专门处理的查询类型**:
• "查询 @nibfe/crm-pc-react-components 中的 AiInviteModal 组件"
• "@company/ui-lib 的 Button 组件怎么用"
• "查找 @scope/package-name 文档"
• "xx包中的yy组件API"

💡 **智能判断说明**: 
- 对于"@nibfe/crm-pc-react-components"等包名查询 → **强制使用此工具**
- 对于"Button组件"、"表格组件"等组件名查询 → AI自主判断最合适的工具
- 对于"utils工具类"、"config配置"等工具类查询 → AI自主判断最合适的工具

💡 **查询策略**:
• 如果指定npm包名，优先从本地项目docs目录查找
• 如果指定组件名，精确查询对应官方文档
• 如果指定文档源，优先从该官方源查询
• 支持跨文档源的智能推荐和对比

🔍 **典型使用场景**: 
• "查询 @nibfe/crm-pc-react-components 中的 AiInviteModal 组件"（**最高优先级**）
• "Antd Button组件怎么用？"
• "React Router官方文档"
• "Element UI表格组件API"
• "Vue3组合式API官方指南"

📖 **查询示例**:
• **NPM包组件查询**: ["AiInviteModal"], sources: ["@nibfe/crm-pc-react-components"]
• 查询官方组件: ["Button", "Table", "Form", "Select"]
• 查询框架文档: ["React Hooks", "Vue Composition"]  
• 查询UI库: ["Antd", "Element", "Material-UI"]`;

  if (resourceDescriptions.length === 0) {
    return `${baseDescription}

⚠️ **外部文档源未配置**: 
当前未配置第三方文档源，请使用 --source 参数配置官方文档源，如:
• --source antd（Ant Design官方文档）
• --source react（React官方文档）
• --source vue（Vue官方文档）`;
  }

  const sourcesDescription = `📚 **已配置官方文档源** (${resourceDescriptions.length}个):
${resourceDescriptions
  .map(
    (resource, index) =>
      `${index + 1}. **${resource.source}**: ${resource.description}${
        resource.components.length > 0
          ? `\n   📦 可查询组件: ${resource.components.slice(0, 5).join(', ')}${resource.components.length > 5 ? '...' : ''}`
          : ''
      }`
  )
  .join('\n')}`;

  return `${baseDescription}

${sourcesDescription}`;
}

/**
 * 构建外部文档查询响应文本
 */
function buildExtDocsResponse(
  results: ExtDocContent[],
  extDocs?: string[],
  sources?: string[]
): string {
  if (results.length === 0) {
    return `# 远端文档查询结果

❌ 未找到相关文档

${extDocs ? `查询的组件：${extDocs.join(', ')}` : ''}
${sources ? `指定的文档源：${sources.join(', ')}` : ''}

💡 建议：
- 检查组件名称是否正确
- 尝试使用更通用的关键词
- 确认文档源是否可用`;
  }

  const groupedResults = groupResultsBySource(results);

  return `# 远端文档查询结果1

📊 查询统计：找到 ${results.length} 个相关文档，来源 ${Object.keys(groupedResults).length} 个文档库

${Object.entries(groupedResults)
  .map(
    ([source, docs]) => `
## 📚 ${source}

${docs
  .map(
    doc => `
### ${getTypeIcon(doc.type)} ${doc.title}

${doc.description ? `**简介：** ${doc.description}` : ''}

${doc.content}

---
`
  )
  .join('')}
`
  )
  .join('')}

💡 提示：如需获取特定组件的完整文档，请指定具体的组件名称。`;
}

/**
 * 按文档源分组结果
 */
function groupResultsBySource(
  results: ExtDocContent[]
): Record<string, ExtDocContent[]> {
  return results.reduce(
    (groups, doc) => {
      const source = doc.source || 'Unknown';
      if (!groups[source]) {
        groups[source] = [];
      }
      groups[source].push(doc);
      return groups;
    },
    {} as Record<string, ExtDocContent[]>
  );
}

/**
 * 根据文档类型获取图标
 */
function getTypeIcon(type: string): string {
  switch (type) {
    case 'component':
      return '🧩';
    case 'method':
      return '⚙️';
    case 'guide':
      return '📖';
    default:
      return '📄';
  }
}
