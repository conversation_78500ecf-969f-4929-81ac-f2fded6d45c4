/**
 * NPM文档查询工具
 * 支持查询NPM包的文档和依赖信息，提供包版本、描述、使用方法等信息
 */
import { z } from "zod";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import * as fs from "fs";
import * as path from "path";
import { logger } from "../utils/logger.js";
import { handleError, ErrorCode, MCPError } from "../utils/errors.js";

/**
 * npm包信息接口
 */
interface NpmPackageInfo {
  name: string;
  version: string;
  description?: string;
  homepage?: string;
  repository?: {
    type: string;
    url: string;
  };
  keywords?: string[];
  license?: string;
  readme?: string;
  readmeSource?: string;
}

/**
 * npm注册表API响应接口
 */
interface NpmRegistryResponse {
  name: string;
  version: string;
  description?: string;
  homepage?: string;
  repository?: {
    type: string;
    url: string;
  };
  keywords?: string[];
  license?: string;
  readme?: string;
  versions: Record<string, any>;
}

/**
 * HTTP请求工具函数（替代fetch，避免兼容性问题）
 */
async function makeHttpRequest(url: string, options: {
  timeout?: number;
} = {}): Promise<Response> {
  const { timeout = 10000 } = options;

  if (typeof fetch === 'undefined') {
    throw new MCPError(
      ErrorCode.NETWORK_ERROR,
      'HTTP请求功能不可用，请确保运行环境支持fetch'
    );
  }

  try {
    const response = await fetch(url, {
      signal: AbortSignal.timeout?.(timeout),
    });

    return response;
  } catch (error) {
    if (error instanceof Error) {
      throw new MCPError(
        ErrorCode.NETWORK_ERROR,
        `网络请求失败: ${error.message}`,
        error
      );
    }
    throw error;
  }
}

/**
 * npm包文档查询客户端
 */
class NpmDocsClient {
  private registryUrl: string = 'https://registry.npmjs.org';

  /**
   * 从本地node_modules读取README文件
   * @param packageName 包名
   * @param projectRoot 项目根目录
   */
  private readLocalReadme(packageName: string, projectRoot: string): string | null {
    try {
      const packagePath = path.join(projectRoot, 'node_modules', packageName);

      // 常见的README文件名列表
      const readmeFiles = [
        'README.md',
        'readme.md', 
        'README.txt',
        'readme.txt',
        'README',
        'readme'
      ];

      logger.debug(`尝试从本地读取README`, { packageName, packagePath });

      for (const readmeFile of readmeFiles) {
        const readmePath = path.join(packagePath, readmeFile);
        if (fs.existsSync(readmePath)) {
          try {
            const content = fs.readFileSync(readmePath, 'utf-8');
            logger.info(`成功从本地读取README`, { 
              packageName, 
              readmeFile, 
              contentLength: content.length 
            });
            return content;
          } catch (error) {
            logger.warn(`读取本地README文件失败`, { 
              packageName, 
              readmePath, 
              error: error instanceof Error ? error.message : String(error) 
            });
          }
        }
      }

      logger.debug(`未找到本地README文件`, { packageName, packagePath, searchedFiles: readmeFiles });
      return null;
    } catch (error) {
      logger.warn(`读取本地README时发生错误`, { 
        packageName, 
        projectRoot, 
        error: error instanceof Error ? error.message : String(error) 
      });
      return null;
    }
  }

  /**
   * 从npm注册表获取包信息
   */
  async getPackageInfo(packageName: string, projectRoot?: string): Promise<NpmPackageInfo | null> {
    try {
      const url = `${this.registryUrl}/${encodeURIComponent(packageName)}`;
      logger.debug(`查询NPM包: ${packageName}`, { url });

      const response = await makeHttpRequest(url, { timeout: 8000 });

      if (!response.ok) {
        if (response.status === 404) {
          logger.debug(`NPM包不存在于公开注册表: ${packageName}，尝试从本地获取信息`);

          // 对于私有包，尝试从本地构建包信息
          if (projectRoot) {
            return this.buildLocalPackageInfo(packageName, projectRoot);
          }

          return null; // 没有项目根目录，无法获取本地信息
        }
        throw new MCPError(
          ErrorCode.NETWORK_ERROR,
          `NPM API请求失败: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json() as NpmRegistryResponse;

      // 获取最新版本信息
      const latestVersion = data.version || Object.keys(data.versions).pop();
      const versionData = latestVersion ? data.versions[latestVersion] : data;

      let readme = data.readme;
      let readmeSource = 'NPM API';

      // 如果NPM API没有提供README，尝试从本地node_modules读取
      if ((!readme || readme.trim().length === 0) && projectRoot) {
        const localReadme = this.readLocalReadme(packageName, projectRoot);
        if (localReadme) {
          readme = localReadme;
          readmeSource = 'Local node_modules';
        }
      }

      const packageInfo = {
        name: data.name,
        version: latestVersion || 'unknown',
        description: versionData.description || data.description,
        homepage: versionData.homepage || data.homepage,
        repository: versionData.repository || data.repository,
        keywords: versionData.keywords || data.keywords,
        license: versionData.license || data.license,
        readme,
        readmeSource,
      };

      logger.debug(`NPM包查询成功: ${packageName} v${latestVersion}`, { 
        readmeSource,
        hasReadme: !!readme,
        readmeLength: readme?.length || 0
      });
      return packageInfo;
    } catch (error) {
      const mcpError = handleError(error, { 
        operation: 'getPackageInfo', 
        packageName,
        registryUrl: this.registryUrl 
      });
      logger.error(`NPM包查询失败: ${packageName}`, mcpError);
      return null;
    }
  }

  /**
   * 从本地信息构建包信息（用于私有包）
   */
  private buildLocalPackageInfo(packageName: string, projectRoot: string): NpmPackageInfo | null {
    try {
      // 1. 尝试从项目package.json获取版本信息
      const packageJsonPath = path.join(projectRoot, 'package.json');
      let version = 'unknown';

      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
        const allDeps = {
          ...packageJson.dependencies,
          ...packageJson.devDependencies,
          ...packageJson.peerDependencies
        };
        version = allDeps[packageName] || 'unknown';
      }

      // 2. 尝试从本地包的package.json获取详细信息
      const localPackagePath = path.join(projectRoot, 'node_modules', packageName);
      const localPackageJsonPath = path.join(localPackagePath, 'package.json');

      let description = '';
      let homepage = '';
      let repository = undefined;
      let keywords: string[] = [];
      let license = '';

      if (fs.existsSync(localPackageJsonPath)) {
        const localPackageJson = JSON.parse(fs.readFileSync(localPackageJsonPath, 'utf-8'));
        description = localPackageJson.description || '';
        homepage = localPackageJson.homepage || '';
        repository = localPackageJson.repository;
        keywords = localPackageJson.keywords || [];
        license = localPackageJson.license || '';

        // 如果有确切的版本信息，使用本地的
        if (localPackageJson.version) {
          version = localPackageJson.version;
        }
      }

      // 3. 尝试读取本地README
      const localReadme = this.readLocalReadme(packageName, projectRoot);

      if (!fs.existsSync(localPackagePath)) {
        logger.debug(`本地包目录不存在: ${localPackagePath}`);
        return null;
      }

      const packageInfo: NpmPackageInfo = {
        name: packageName,
        version,
        description: description || `私有包: ${packageName}`,
        homepage,
        repository,
        keywords,
        license,
        readme: localReadme || undefined,
        readmeSource: localReadme ? 'Local node_modules' : undefined,
      };

      logger.info(`从本地构建私有包信息: ${packageName}`, { 
        version,
        hasReadme: !!localReadme,
        readmeLength: localReadme?.length || 0
      });

      return packageInfo;
    } catch (error) {
      logger.warn(`构建本地包信息失败: ${packageName}`, { 
        error: error instanceof Error ? error.message : String(error) 
      });
      return null;
    }
  }

  /**
   * 解析package.json获取项目依赖
   * @param projectRoot 项目根目录
   */
  getProjectDependencies(projectRoot: string): string[] {
    try {
      const packageJsonPath = path.join(projectRoot, 'package.json');

      logger.debug(`尝试读取目标项目的package.json`, { packageJsonPath, projectRoot });

      if (!fs.existsSync(packageJsonPath)) {
        logger.warn(`package.json文件不存在`, { packageJsonPath });
        return [];
      }

      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
      const dependencies = new Set<string>();

      // 收集所有类型的依赖
      const depTypes = ['dependencies', 'devDependencies', 'peerDependencies'];
      const depCounts: Record<string, number> = {};

      depTypes.forEach(depType => {
        if (packageJson[depType]) {
          const deps = Object.keys(packageJson[depType]);
          deps.forEach(dep => dependencies.add(dep));
          depCounts[depType] = deps.length;
          logger.debug(`发现${depType}`, { count: deps.length, packages: deps.slice(0, 5) });
        }
      });

      const allDeps = Array.from(dependencies);
      logger.info(`读取项目依赖成功`, { 
        projectRoot, 
        packageJsonPath,
        totalCount: allDeps.length,
        depCounts,
        samplePackages: allDeps.slice(0, 10) // 显示前10个包作为样本
      });

      return allDeps;
    } catch (error) {
      const mcpError = handleError(error, { 
        operation: 'getProjectDependencies', 
        projectRoot 
      });
      logger.error('读取package.json失败', mcpError);
      return [];
    }
  }

  /**
   * 过滤掉已有文档的npm包（精确匹配，避免误过滤）
   */
  filterPackagesWithoutDocs(packages: string[], documentedPackages: string[] = []): string[] {
    const documentedSet = new Set(documentedPackages.map(pkg => pkg.toLowerCase()));
    return packages.filter(pkg => !documentedSet.has(pkg.toLowerCase()));
  }
}

/**
 * 注册npm包文档查询工具
 * @param server MCP服务器实例
 * @param projectRoot 项目根目录（从配置传入，与其他工具保持一致）
 */
export function registerNpmDocsQueryTool(server: McpServer, projectRoot: string) {
  const npmClient = new NpmDocsClient();
  const projectDependencies = npmClient.getProjectDependencies(projectRoot);

  // 定义工具参数类型
  interface NpmDocsToolParams {
    packageNames?: string[];
    includeReadme?: boolean;
    filterWellKnown?: boolean;
  }

  server.tool(
    'search_npm_docs',
    `📦 项目依赖包文档智能搜索工具 | package.json依赖包查询专家

🎯 **核心功能**:
• 🔍 智能搜索项目package.json中的依赖包文档
• 📚 获取包的README、版本信息和使用示例
• 🔗 提供npm主页、GitHub仓库链接
• ⚡ 自动过滤知名包，专注于项目特定依赖
• 📖 支持从本地node_modules读取README（**私有包专用功能**）

📦 **NPM包查询优先级**（仅针对包名格式查询）:
当用户明确提到npm包格式时（如@scope/package-name），此工具作为第二选择：
1. search_external_docs 优先 - 查找官方文档
2. **本工具作为补充** - 当官方文档不可用时查找项目依赖
3. search_local_docs 最后 - 查找项目内部文档

💡 **智能判断说明**:
- 对于组件名查询（如"Button"、"Table"）→ AI根据上下文自主选择工具
- 对于工具类查询（如"utils"、"helper"）→ AI根据上下文自主选择工具  
- 对于包名查询（如"@nibfe/dz-form-core"）→ 按上述优先级作为第二选择

📊 **项目依赖概览**: 
• 总依赖包数量: ${projectDependencies.length}
• 可查询包数量: ${projectDependencies.length > 0 ? '全部' : '0'}
• 数据来源: 本地package.json + npm注册表 + 本地node_modules

🎯 **最佳使用场景**:
• 查询项目中已安装的npm包文档
• 分析项目依赖关系和版本信息
• 查找私有包或内部包的文档信息
• 了解项目技术栈和依赖构成

💡 **查询模式**:
• **精确包名查询**: 指定npm包名获取详细文档
• **项目依赖概览**: 不指定包名查看所有项目依赖
• **智能过滤模式**: 自动排除React、Vue等知名包
• **依赖分析模式**: 显示包的版本、许可证、依赖关系

🚨 **特别适用场景**:
• "这个项目用了什么版本的lodash？"
• "我们的私有包@nibfe/dz-form-core有什么功能？"
• "项目依赖了哪些UI组件库？"
• "这个内部包的README文档在哪？"

📋 **常见包类型**:
• 🛠️ 工具库: lodash, dayjs, ramda
• 🎨 UI组件: 内部组件库、定制组件
• ⚙️ 构建工具: webpack插件、babel插件
• 🧪 测试框架: jest插件、自定义测试工具
• 📦 私有包: @company/internal-lib, @nibfe/dz-form-core

🔍 **使用场景**: 项目依赖分析、内部包文档查询、依赖包迁移指南、技术栈了解`,
    {
      packageNames: z.array(z.string()).optional().describe('📝 要查询的npm包名称列表。例如: ["axios", "lodash", "dayjs"] 或留空查询所有依赖'),
      includeReadme: z.boolean().optional().default(true).describe('📖 是否包含完整README内容（默认true）'),
      filterWellKnown: z.boolean().optional().default(true).describe('🎯 是否过滤知名包如React、Vue等（默认true）'),
    },
    async (params: NpmDocsToolParams) => {
      const { packageNames, includeReadme = true, filterWellKnown = true } = params;
      try {
        logger.info(`NPM包文档查询开始`, { 
          projectRoot, 
          packageNames, 
          includeReadme, 
          filterWellKnown,
          totalProjectDependencies: projectDependencies.length
        });
        let packagesToQuery: string[] = [];

        if (packageNames && packageNames.length > 0) {
          // 查询指定的包
          packagesToQuery = packageNames;
          logger.debug(`使用指定包名查询`, { packageNames });
        } else {
          // 查询项目中的所有依赖
          packagesToQuery = projectDependencies;
          logger.debug(`使用项目依赖查询`, { 
            count: projectDependencies.length,
            samplePackages: projectDependencies.slice(0, 10)
          });

                  if (filterWellKnown) {
          // 过滤掉知名的已有文档的包
          const documentedPackages = [
            'react', 'vue', 'angular', 'express', 'lodash', 
            'moment', 'axios', 'jquery', 'bootstrap', 'webpack'
          ];
          const beforeFilter = packagesToQuery.length;
          packagesToQuery = npmClient.filterPackagesWithoutDocs(packagesToQuery, documentedPackages);
          const afterFilter = packagesToQuery.length;
          logger.debug(`知名包过滤`, { 
            beforeFilter, 
            afterFilter, 
            filtered: beforeFilter - afterFilter,
            documentedPackages 
          });
        }
        }

        // 限制查询数量，避免响应过大
        const maxPackages = 20;
        if (packagesToQuery.length > maxPackages) {
          packagesToQuery = packagesToQuery.slice(0, maxPackages);
        }

        const packageInfos: NpmPackageInfo[] = [];
        const failedPackages: string[] = [];

        // 并发查询包信息
        const queryPromises = packagesToQuery.map(async (packageName) => {
          const info = await npmClient.getPackageInfo(packageName, projectRoot);
          if (info) {
            packageInfos.push(info);
          } else {
            failedPackages.push(packageName);
          }
        });

        await Promise.all(queryPromises);

        // 构建响应文本
        const responseText = buildNpmDocsResponse(
          packageInfos, 
          failedPackages, 
          packageNames, 
          includeReadme,
          projectDependencies.length,
          projectRoot
        );

        return {
          content: [
            {
              type: 'text',
              text: responseText
            }
          ]
        };
      } catch (error) {
        const mcpError = handleError(error, { 
          operation: 'npmDocsQuery',
          projectRoot,
          packageNames 
        });
        logger.error('NPM包文档查询失败', mcpError);

        return {
          isError: true,
          content: [
            {
              type: 'text',
              text: `查询npm包文档时发生错误: ${mcpError.message}

🔍 **调试信息**:
- 项目根目录: \`${projectRoot}\`
- 查询包: ${packageNames ? packageNames.join(', ') : '所有依赖'}
- 错误代码: ${mcpError.code}

💡 **建议**: 请检查网络连接和包名称是否正确。`,
            }
          ]
        }
      }
    }
  );
}

/**
 * 构建npm包文档查询响应文本
 */
function buildNpmDocsResponse(
  packageInfos: NpmPackageInfo[],
  failedPackages: string[],
  packageNames?: string[],
  includeReadme: boolean = true,
  totalDependencies?: number,
  projectRoot?: string
): string {
  const header = `# 📦 NPM包文档查询结果

## 🔍 查询信息
${projectRoot ? `- 🏠 **项目根目录**: \`${projectRoot}\`` : ''}
- 📁 **当前工作目录**: \`${process.cwd()}\`
- ⏰ **查询时间**: ${new Date().toLocaleString()}

## 📊 查询统计
- 🎯 **查询包**: ${packageNames ? packageNames.join(', ') : '项目所有依赖'}
- ✅ **成功查询**: ${packageInfos.length} 个包
- ❌ **查询失败**: ${failedPackages.length} 个包
${totalDependencies ? `- 📦 **项目总依赖**: ${totalDependencies} 个包` : ''}`;

  const successSection = packageInfos.length > 0 ? `
## 📦 包信息详情

${packageInfos.map(pkg => `
### 📌 ${pkg.name} v${pkg.version}

${pkg.description ? `**描述：** ${pkg.description}` : ''}

${pkg.keywords && pkg.keywords.length > 0 ? `**关键词：** ${pkg.keywords.join(', ')}` : ''}

${pkg.license ? `**许可证：** ${pkg.license}` : ''}

${pkg.homepage ? `**主页：** ${pkg.homepage}` : ''}

${pkg.repository ? `**仓库：** ${pkg.repository.url}` : ''}

${includeReadme && pkg.readme ? `
**README文档${pkg.readmeSource ? ` (来源: ${pkg.readmeSource})` : ''}：**

${pkg.readme.length > 5000 ? pkg.readme.substring(0, 5000) + '\n\n... (README内容过长，已截断)' : pkg.readme}
` : ''}

---
`).join('')}
` : '';

  const failedSection = failedPackages.length > 0 ? `
## ❌ 查询失败的包

${failedPackages.map(pkg => `- ${pkg}`).join('\n')}

` : '';

  const footer = `
💡 使用提示：
- 使用 packageNames 参数可以查询特定的npm包
- 设置 includeReadme=false 可以只获取包的基本信息
- 设置 filterWellKnown=false 可以查询所有依赖包`;

  return header + successSection + failedSection + footer;
} 