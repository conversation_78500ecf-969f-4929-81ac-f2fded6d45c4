/**
 * 本地文档查询工具
 * 支持查询项目内部存储的文档资源，按组件名称批量查阅文档
 * 重构版本：接收预扫描的文档数据，避免重复扫描
 */
import { z } from "zod";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { ProjectDocumentManager, DocumentIndex } from "../utils/documentManager.js";
import { logger } from "../utils/logger.js";
import { handleError, ErrorCode, MCPError } from "../utils/errors.js";

/**
 * 查询结果接口定义
 */
interface QueryResult {
  query: string;
  found: boolean;
  count: number;
  documents: DocumentSummary[];
  suggestions?: string[];
  message: string;
}

/**
 * 文档摘要接口定义
 */
interface DocumentSummary {
  title: string;
  description: string;
  category: string;
  relativePath: string;
  size: string;
  lastModified: string;
}

/**
 * 工具参数类型定义
 */
interface LocalDocsToolParams {
  docNames: string[];
}

/**
 * 关键词处理器映射
 */
const keywordHandlers = {
  all: ['全部', '所有', 'all'],
  components: ['组件', 'components', '存量组件'],
  utils: ['工具', 'utils', 'tools', '工具类'],
  api: ['api', 'API', '接口']
};

/**
 * 处理特殊关键词查询
 */
function handleSpecialKeyword(
  docName: string, 
  documentManager: ProjectDocumentManager
): QueryResult | null {
  const lowerDocName = docName.toLowerCase();
  
  if (keywordHandlers.all.includes(lowerDocName)) {
    const allDocs = documentManager.getAllDocuments();
    return {
      query: docName,
      found: true,
      count: allDocs.length,
      documents: allDocs.slice(0, 20).map(formatDocumentSummary),
      message: `找到所有 ${allDocs.length} 个文档（显示前20个）`
    };
  }

  if (keywordHandlers.components.includes(lowerDocName)) {
    const componentDocs = documentManager.searchDocuments('组件', { limit: 20 });
    return {
      query: docName,
      found: true,
      count: componentDocs.length,
      documents: componentDocs.map(formatDocumentSummary),
      message: `找到 ${componentDocs.length} 个组件相关文档`
    };
  }

  if (keywordHandlers.utils.includes(lowerDocName)) {
    const utilDocs = documentManager.searchDocuments('工具', { limit: 20 });
    return {
      query: docName,
      found: true,
      count: utilDocs.length,
      documents: utilDocs.map(formatDocumentSummary),
      message: `找到 ${utilDocs.length} 个工具相关文档`
    };
  }

  if (keywordHandlers.api.includes(docName)) {
    const apiDocs = documentManager.searchDocuments('API', { limit: 20 });
    return {
      query: docName,
      found: true,
      count: apiDocs.length,
      documents: apiDocs.map(formatDocumentSummary),
      message: `找到 ${apiDocs.length} 个API相关文档`
    };
  }

  return null;
}

/**
 * 注册本地文档查询工具
 * @param server MCP服务器实例
 * @param documentManager 文档管理器实例（已完成扫描）
 */
export function registerLocalDocsQueryTool(server: McpServer, documentManager: ProjectDocumentManager) {
  // 生成动态工具描述
  const toolDescription = documentManager.generateToolDescription();

  server.tool(
    'search_local_docs',
    toolDescription,
    {
      docNames: z.array(z.string()).describe('📝 要查询的文档/组件/工具类名称列表。支持关键词: ["组件", "工具", "API", "存量", "全部"] 或具体名称: ["Button", "utils", "config"]'),
    },
    async (params: LocalDocsToolParams) => {
      const { docNames } = params;
      try {
        logger.info(`本地文档查询开始`, { 
          docNames, 
          queryTime: new Date().toISOString() 
        });
        
        const results: QueryResult[] = [];
        const stats = documentManager.getStats();
        
        // 遍历所有请求的文档关键词
        for (const docName of docNames) {
          // 尝试特殊关键词处理
          const specialResult = handleSpecialKeyword(docName, documentManager);
          if (specialResult) {
            results.push(specialResult);
            continue;
          }

          // 普通关键词搜索
          const searchResults = documentManager.searchDocuments(docName, { 
            limit: 10, 
            includeContent: false 
          });

          if (searchResults.length > 0) {
            results.push({
              query: docName,
              found: true,
              count: searchResults.length,
              documents: searchResults.map(formatDocumentSummary),
              message: `找到 ${searchResults.length} 个匹配文档`
            });
          } else {
            // 尝试模糊匹配
            const fuzzyResults = documentManager.searchDocuments(docName.substring(0, 3), { 
              limit: 5 
            });
            
            results.push({
              query: docName,
              found: false,
              count: 0,
              documents: [],
              suggestions: fuzzyResults.map(doc => doc.title),
              message: `未找到匹配文档"${docName}"。${fuzzyResults.length > 0 ? `建议查看：${fuzzyResults.map(d => d.title).join(', ')}` : ''}`
            });
          }
        }

        // 如果有具体文档需要显示完整内容
        const detailResults = [];
        for (const result of results) {
          if (result.found && result.documents.length <= 3) {
            // 对于少量文档，显示完整内容
            for (const docSummary of result.documents) {
              const fullDoc = documentManager.getDocument(docSummary.relativePath);
              if (fullDoc) {
                detailResults.push({
                  ...docSummary,
                  content: fullDoc.content,
                  sections: extractSections(fullDoc.content)
                });
              }
            }
          }
        }

        // 构建响应文本
        const responseText = buildResponseText(results, detailResults, stats, documentManager);

        return {
          content: [
            {
              type: 'text',
              text: responseText
            }
          ]
        };
      } catch (error) {
        const mcpError = handleError(error, { 
          operation: 'localDocsQuery',
          docNames 
        });
        logger.error('本地文档查询失败', mcpError);
        
        return {
          isError: true,
          content: [
            {
              type: 'text',
              text: `❌ 本地文档查询失败: ${mcpError.message}

🔍 **调试信息**:
- 查询关键词: ${docNames.join(', ')}
- 查询时间: ${new Date().toLocaleString()}
- 错误代码: ${mcpError.code}

💡 **建议**: 请检查查询关键词是否正确，或尝试使用更通用的关键词如"组件"、"工具"、"API"等。`,
            }
          ]
        }
      }
    }
  );
}

/**
 * 格式化文档摘要信息
 */
function formatDocumentSummary(doc: DocumentIndex): DocumentSummary {
  return {
    title: doc.title,
    description: doc.description,
    category: doc.category,
    relativePath: doc.relativePath,
    size: `${(doc.size / 1024).toFixed(2)} KB`,
    lastModified: doc.lastModified.toLocaleDateString()
  };
}

/**
 * 从文档内容中提取章节
 */
function extractSections(content: string): string[] {
  const sections = [];
  const lines = content.split('\n');
  
  for (const line of lines) {
    const trimmed = line.trim();
    if (trimmed.startsWith('#')) {
      const level = trimmed.match(/^#+/)?.[0].length || 0;
      const title = trimmed.substring(level).trim();
      if (title) {
        sections.push(`${'  '.repeat(level - 1)}• ${title}`);
      }
    }
  }
  
  return sections;
}

/**
 * 构建响应文本
 */
function buildResponseText(
  results: any[], 
  detailResults: any[], 
  stats: any, 
  documentManager: ProjectDocumentManager
): string {
  const structure = documentManager.getDocumentStructure();
  
  let responseText = `# 📁 本地文档查询结果

🎯 **核心功能**: 智能搜索项目中的所有 Markdown 文档
📊 **扫描统计**: 共 ${stats.totalDocuments} 个文档，${stats.totalCategories} 个分类，总大小 ${(stats.totalSize / 1024).toFixed(2)} KB

🔍 **主要使用场景**: 
当用户询问**项目内部资源**时使用此工具：
• "当前仓库有哪些存量组件"
• "项目有什么组件/工具类" 
• "查看项目文档/API文档"
• "项目规范/开发指南"
• "使用AI友好文档工具"

📦 **NPM包查询时的定位**（仅针对包名格式查询）:
当用户提到npm包格式时（如@scope/package-name），此工具为最后选择：
1. search_external_docs 优先 - 查找官方文档
2. search_npm_docs 次之 - 查找项目依赖
3. **本工具最后** - 查找项目内部文档（可能包含相关说明）

💡 **智能判断说明**:
- 对于组件名查询（如"Button"、"表格组件"）→ AI自主判断（可能优先选择本工具）
- 对于工具类查询（如"utils"、"helper"）→ AI自主判断（可能优先选择本工具）
- 对于项目资源查询（如"存量组件"）→ 直接使用本工具
- 对于包名查询（如"@nibfe/dz-form-core"）→ 按上述优先级作为最后选择

⚠️ **避免误用**: 
- 不要将此工具用于第三方组件库查询
- 不要将此工具用于npm包文档查询
- 主要用于项目内部组件、工具类、文档的查询

## 📊 项目文档概览
- 📚 **文档总数**: ${stats.totalDocuments} 个
- 📂 **分类数量**: ${stats.totalCategories} 个
- 💾 **总大小**: ${(stats.totalSize / 1024).toFixed(2)} KB
- ⏰ **查询时间**: ${new Date().toLocaleString()}

## 🔍 查询结果摘要
`;

  // 显示查询结果摘要
  for (const result of results) {
    if (result.found) {
      responseText += `✅ **${result.query}**: ${result.message}\n`;
    } else {
      responseText += `❌ **${result.query}**: ${result.message}\n`;
    }
  }

  responseText += `\n## 📋 文档详情\n\n`;

  // 显示找到的文档列表
  for (const result of results) {
    if (result.found && result.documents.length > 0) {
      responseText += `### 🔖 "${result.query}" 相关文档\n\n`;
      
      for (const doc of result.documents) {
        responseText += `#### 📄 ${doc.title}
- **路径**: \`${doc.relativePath}\`
- **分类**: ${doc.category}
- **描述**: ${doc.description}
- **大小**: ${doc.size}
- **更新**: ${doc.lastModified}

`;
      }
    }
  }

  // 显示完整文档内容（如果有的话）
  if (detailResults.length > 0) {
    responseText += `\n## 📖 文档完整内容\n\n`;
    
    for (const doc of detailResults) {
      responseText += `### 📑 ${doc.title}

**文档结构**:
${doc.sections.join('\n')}

**文档内容**:
\`\`\`markdown
${doc.content}
\`\`\`

---

`;
    }
  }

  // 显示文档结构概览
  responseText += `\n## 🗂️ 项目文档结构\n\n`;
  responseText += generateStructureOverview(structure, 0);

  responseText += `\n---

💡 **查询提示**: 
- 使用 "组件"、"工具"、"API" 等关键词可以快速定位相关文档
- 使用 "全部" 可以查看所有文档列表
- 支持文件名、标题、描述的模糊匹配`;

  return responseText;
}

/**
 * 生成文档结构概览
 */
function generateStructureOverview(structure: any, depth: number): string {
  let result = '';
  const indent = '  '.repeat(depth);

  for (const [category, data] of Object.entries(structure)) {
    const categoryData = data as any; // 类型断言
    
    if (category === 'root') {
      if (categoryData.docs && categoryData.docs.length > 0) {
        result += `${indent}📄 **根目录** (${categoryData.docs.length}个文档)\n`;
      }
    } else {
      result += `${indent}📁 **${category}** (${categoryData.docs ? categoryData.docs.length : 0}个文档)\n`;
      
      // 递归处理子分类
      if (categoryData.subcategories) {
        result += generateStructureOverview(categoryData.subcategories, depth + 1);
      }
    }
  }

  return result;
} 