/**
 * 项目组件和工具类扫描工具
 * 专门用于检测当前仓库中的存量组件、工具函数等资源
 */
import { z } from "zod";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import * as fs from "fs";
import * as path from "path";
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 组件信息接口
 */
interface ComponentInfo {
  name: string;
  type: 'component' | 'utility' | 'hook' | 'service' | 'type' | 'constant';
  filePath: string;
  description?: string;
  exports?: string[];
  imports?: string[];
  size: number;
}

/**
 * 项目扫描结果
 */
interface ProjectScanResult {
  components: ComponentInfo[];
  totalFiles: number;
  projectStructure: string[];
  summary: {
    componentCount: number;
    utilityCount: number;
    hookCount: number;
    serviceCount: number;
    typeCount: number;
    constantCount: number;
  };
}

/**
 * 文件扫描器类
 */
class ProjectScanner {
  private projectRoot: string;
  private excludePatterns: RegExp[];

  constructor(projectRoot: string) {
    this.projectRoot = projectRoot;
    this.excludePatterns = [
      /node_modules/,
      /\.git/,
      /dist/,
      /build/,
      /coverage/,
      /\.next/,
      /\.nuxt/,
      /\.vscode/,
      /\.idea/,
      /\.DS_Store/,
      /\.env/,
      /package-lock\.json/,
      /yarn\.lock/,
      /pnpm-lock\.yaml/
    ];
  }

  /**
   * 扫描项目中的所有组件和工具类
   */
  async scanProject(): Promise<ProjectScanResult> {
    const components: ComponentInfo[] = [];
    const projectStructure: string[] = [];
    
    await this.scanDirectory(this.projectRoot, components, projectStructure, 0);
    
    const summary = this.generateSummary(components);
    
    return {
      components,
      totalFiles: components.length,
      projectStructure,
      summary
    };
  }

  /**
   * 递归扫描目录
   */
  private async scanDirectory(
    dirPath: string, 
    components: ComponentInfo[], 
    structure: string[], 
    depth: number
  ): Promise<void> {
    if (depth > 8) return; // 限制扫描深度
    
    try {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const relativePath = path.relative(this.projectRoot, fullPath);
        
        // 跳过排除的路径
        if (this.shouldExclude(relativePath)) continue;
        
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          structure.push(`${'  '.repeat(depth)}📁 ${item}/`);
          await this.scanDirectory(fullPath, components, structure, depth + 1);
        } else if (this.isTargetFile(fullPath)) {
          const componentInfo = await this.analyzeFile(fullPath);
          if (componentInfo) {
            components.push(componentInfo);
            structure.push(`${'  '.repeat(depth)}📄 ${item} (${componentInfo.type})`);
          }
        }
      }
    } catch (error) {
      console.warn(`扫描目录失败: ${dirPath}`, error);
    }
  }

  /**
   * 判断是否应该排除文件/目录
   */
  private shouldExclude(relativePath: string): boolean {
    return this.excludePatterns.some(pattern => pattern.test(relativePath));
  }

  /**
   * 判断是否是目标文件类型
   */
  private isTargetFile(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    return ['.ts', '.tsx', '.js', '.jsx', '.vue', '.md'].includes(ext);
  }

  /**
   * 分析文件内容，提取组件信息
   */
  private async analyzeFile(filePath: string): Promise<ComponentInfo | null> {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const relativePath = path.relative(this.projectRoot, filePath);
      const fileName = path.basename(filePath, path.extname(filePath));
      const fileSize = Buffer.byteLength(content, 'utf8');
      
      const componentInfo: ComponentInfo = {
        name: fileName,
        type: this.determineFileType(filePath, content),
        filePath: relativePath,
        size: fileSize,
        exports: this.extractExports(content),
        imports: this.extractImports(content),
        description: this.extractDescription(content)
      };
      
      return componentInfo;
    } catch (error) {
      console.warn(`分析文件失败: ${filePath}`, error);
      return null;
    }
  }

  /**
   * 确定文件类型
   */
  private determineFileType(filePath: string, content: string): ComponentInfo['type'] {
    const fileName = path.basename(filePath).toLowerCase();
    const ext = path.extname(filePath).toLowerCase();
    
    // 根据文件名模式判断
    if (fileName.includes('component') || fileName.includes('comp') || 
        (ext === '.tsx' && content.includes('JSX.Element')) ||
        (ext === '.vue') || content.includes('export default function') ||
        content.includes('export const') && content.includes('React.FC')) {
      return 'component';
    }
    
    if (fileName.includes('hook') || fileName.startsWith('use') || 
        content.includes('useState') || content.includes('useEffect')) {
      return 'hook';
    }
    
    if (fileName.includes('util') || fileName.includes('helper') || 
        fileName.includes('common') || filePath.includes('/utils/')) {
      return 'utility';
    }
    
    if (fileName.includes('service') || fileName.includes('api') || 
        filePath.includes('/service') || filePath.includes('/api/')) {
      return 'service';
    }
    
    if (fileName.includes('type') || fileName.includes('interface') || 
        content.includes('interface ') || content.includes('type ')) {
      return 'type';
    }
    
    if (fileName.includes('const') || content.includes('export const') && 
        !content.includes('function')) {
      return 'constant';
    }
    
    return 'utility'; // 默认归类为工具类
  }

  /**
   * 提取导出信息
   */
  private extractExports(content: string): string[] {
    const exports: string[] = [];
    const exportRegex = /export\s+(?:default\s+)?(?:function\s+|const\s+|class\s+|interface\s+|type\s+)?(\w+)/g;
    let match;
    
    while ((match = exportRegex.exec(content)) !== null) {
      exports.push(match[1]);
    }
    
    return exports;
  }

  /**
   * 提取导入信息
   */
  private extractImports(content: string): string[] {
    const imports: string[] = [];
    const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }
    
    return imports;
  }

  /**
   * 提取文件描述
   */
  private extractDescription(content: string): string {
    // 提取文件开头的注释作为描述
    const commentMatch = content.match(/^\s*\/\*\*\s*\n\s*\*\s*(.+?)\s*\n/);
    if (commentMatch) {
      return commentMatch[1];
    }
    
    // 提取单行注释
    const singleCommentMatch = content.match(/^\s*\/\/\s*(.+)$/m);
    if (singleCommentMatch) {
      return singleCommentMatch[1];
    }
    
    return '暂无描述';
  }

  /**
   * 生成统计摘要
   */
  private generateSummary(components: ComponentInfo[]) {
    return {
      componentCount: components.filter(c => c.type === 'component').length,
      utilityCount: components.filter(c => c.type === 'utility').length,
      hookCount: components.filter(c => c.type === 'hook').length,
      serviceCount: components.filter(c => c.type === 'service').length,
      typeCount: components.filter(c => c.type === 'type').length,
      constantCount: components.filter(c => c.type === 'constant').length
    };
  }
}

/**
 * 注册存量组件扫描工具
 * @param server MCP服务器实例
 * @param projectRoot 项目根目录
 */
export function registerComponentScannerTool(server: McpServer, projectRoot: string) {
  const scanner = new ProjectScanner(projectRoot);

  server.tool(
    'scan_project_components',
    `🧩 存量组件智能扫描器 | 项目资源检测专家

🎯 **专门用于回答"当前仓库有哪些存量组件"类问题**

🚨 **触发关键词** (AI应自动调用):
• "当前仓库有哪些存量组件"
• "项目有什么组件/工具类"
• "扫描项目资源/结构"
• "查看项目中的组件"
• "项目代码结构分析"

🔍 **核心功能**:
• 🧩 **组件检测**: 自动识别React/Vue等组件
• 🔧 **工具类发现**: 扫描utility、helper函数
• 🪝 **Hook检测**: 识别自定义Hook
• 🛠️ **服务类扫描**: API服务、业务逻辑
• 📝 **类型定义**: interface、type等类型文件
• 📊 **常量配置**: 配置文件、常量定义

💡 **智能分析**:
• 📁 生成项目结构树
• 📊 提供详细统计信息
• 🔗 分析文件依赖关系
• 📝 提取文件描述信息
• 📏 计算文件大小统计

📋 **扫描范围**:
• 支持 .ts/.tsx/.js/.jsx/.vue/.md 文件
• 自动排除 node_modules、dist 等无关目录
• 智能识别文件类型和用途
• 递归扫描所有子目录

🎯 **最佳实践**: 当用户询问项目资源、组件清单时，优先使用此工具！`,
    {
      scanDepth: z.number().optional().default(8).describe('🔍 扫描深度限制（默认8层，防止过深扫描）'),
      includeStructure: z.boolean().optional().default(true).describe('📁 是否包含项目结构树（默认true）'),
      filterTypes: z.array(z.enum(['component', 'utility', 'hook', 'service', 'type', 'constant'])).optional().describe('🎯 过滤特定类型文件（可选）'),
    },
    async ({scanDepth = 8, includeStructure = true, filterTypes}) => {
      try {
        console.log(`🔍 开始扫描项目: ${projectRoot}`);
        const result = await scanner.scanProject();
        
        // 如果指定了过滤类型，则过滤结果
        let filteredComponents = result.components;
        if (filterTypes && filterTypes.length > 0) {
          filteredComponents = result.components.filter(comp => 
            filterTypes.includes(comp.type)
          );
        }

        // 构建响应内容，包含项目根目录信息
        const responseText = buildScanResponse(result, filteredComponents, includeStructure, projectRoot);
        
        return {
          content: [
            {
              type: 'text',
              text: responseText
            }
          ]
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error('项目扫描失败:', error);
        return {
          isError: true,
          content: [
            {
              type: 'text',
              text: `❌ 项目扫描失败: ${errorMessage}

🔍 **调试信息**:
- 检测到的项目根目录: \`${projectRoot}\`
- 当前工作目录: \`${process.cwd()}\`

请检查项目路径和权限设置。`
            }
          ]
        };
      }
    }
  );
}

/**
 * 构建扫描结果响应
 */
function buildScanResponse(
  result: ProjectScanResult, 
  filteredComponents: ComponentInfo[], 
  includeStructure: boolean,
  projectRoot: string
): string {
  const { summary, totalFiles, projectStructure } = result;
  
  // 统计信息
  const statsSection = `# 🧩 项目存量组件扫描报告

## 🔍 扫描信息
- 🏠 **项目根目录**: \`${projectRoot}\`
- 📁 **当前工作目录**: \`${process.cwd()}\`
- ⏰ **扫描时间**: ${new Date().toLocaleString()}

## 📊 统计概览
- 📄 **总文件数**: ${totalFiles}
- 🧩 **组件数量**: ${summary.componentCount}
- 🔧 **工具类数**: ${summary.utilityCount}
- 🪝 **Hook数量**: ${summary.hookCount}
- 🛠️ **服务类数**: ${summary.serviceCount}
- 📝 **类型定义**: ${summary.typeCount}
- 📊 **常量配置**: ${summary.constantCount}`;

  // 组件详情
  const componentsSection = `

## 🧩 存量组件详情

${filteredComponents.length === 0 ? '未找到符合条件的组件' : 
  filteredComponents.map((comp, index) => {
    const typeIcon = getTypeIcon(comp.type);
    const sizeKB = (comp.size / 1024).toFixed(2);
    
    return `### ${index + 1}. ${typeIcon} ${comp.name}
- **类型**: ${comp.type}
- **路径**: \`${comp.filePath}\`
- **大小**: ${sizeKB} KB
- **描述**: ${comp.description || '暂无描述'}
${comp.exports && comp.exports.length > 0 ? `- **导出**: ${comp.exports.join(', ')}` : ''}
${comp.imports && comp.imports.length > 0 ? `- **依赖**: ${comp.imports.slice(0, 3).join(', ')}${comp.imports.length > 3 ? '...' : ''}` : ''}`;
  }).join('\n\n')
}`;

  // 项目结构（可选）
  const structureSection = includeStructure ? `

## 📁 项目结构树
\`\`\`
${projectStructure.slice(0, 50).join('\n')}${projectStructure.length > 50 ? '\n... (显示前50项)' : ''}
\`\`\`` : '';

  return statsSection + componentsSection + structureSection + `

## 💡 使用建议
1. **组件复用**: 优先使用现有组件，避免重复开发
2. **工具函数**: 充分利用已有工具类，提高开发效率  
3. **类型安全**: 使用项目中定义的类型，保持代码一致性
4. **架构规范**: 参考现有文件组织方式，保持项目结构清晰

---
📝 **扫描完成时间**: ${new Date().toLocaleString()}`;
}

/**
 * 获取文件类型图标
 */
function getTypeIcon(type: ComponentInfo['type']): string {
  const iconMap = {
    'component': '🧩',
    'utility': '🔧',
    'hook': '🪝',
    'service': '🛠️',
    'type': '📝',
    'constant': '📊'
  };
  return iconMap[type] || '📄';
} 